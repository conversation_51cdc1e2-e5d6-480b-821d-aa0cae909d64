-- 为用户表添加VIP等级字段
-- 执行时间：2024-07-22
-- 目的：支持VIP用户和免费用户的权限区分

-- 1. 添加userTier字段
ALTER TABLE `user` ADD COLUMN `userTier` VARCHAR(20) NOT NULL DEFAULT 'FREE' COMMENT 'VIP等级：FREE/VIP_MONTHLY/VIP_YEARLY/VIP_LIFETIME';

-- 2. 添加索引以优化查询性能
CREATE INDEX `idx_user_tier` ON `user` (`userTier`);

-- 3. 添加字段注释
ALTER TABLE `user` MODIFY COLUMN `userTier` VARCHAR(20) NOT NULL DEFAULT 'FREE' COMMENT 'VIP等级：FREE-免费用户，VIP_MONTHLY-月度VIP，VIP_YEARLY-年度VIP，VIP_LIFETIME-终身VIP';

-- 4. 验证字段添加成功
-- SELECT userTier, COUNT(*) as count FROM user GROUP BY userTier;

-- 5. 可选：为测试创建一些VIP用户（仅在开发环境执行）
-- UPDATE user SET userTier = 'VIP_MONTHLY' WHERE userId IN (1, 2, 3);
-- UPDATE user SET userTier = 'VIP_YEARLY' WHERE userId IN (4, 5);

-- 回滚脚本（如需要）：
-- ALTER TABLE `user` DROP COLUMN `userTier`;
-- DROP INDEX `idx_user_tier` ON `user`;
