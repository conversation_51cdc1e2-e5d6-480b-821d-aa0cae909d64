-- XPrinter数据恢复功能 - 数据库迁移脚本 V1.0
-- 创建时间: 2024-07-18
-- 说明: 为数据恢复功能添加软删除字段和相关表

use xinye_app_v3;

-- =====================================================
-- 第一部分：为现有表添加软删除字段
-- =====================================================

-- 为 templet 表添加软删除字段
ALTER TABLE `templet` 
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间，NULL表示未删除';

-- 为 templet_group 表添加软删除字段
ALTER TABLE `templet_group` 
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间，NULL表示未删除';

-- 为 cloudfile 表添加软删除字段
ALTER TABLE `cloudfile` 
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间，NULL表示未删除';

-- =====================================================
-- 第二部分：创建软删除相关索引
-- =====================================================

-- templet 表索引优化
CREATE INDEX `idx_templet_user_deleted_created` ON `templet`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_deleted_range` ON `templet`(`deleteTime`, `userId`);
CREATE INDEX `idx_templet_delete_time` ON `templet`(`deleteTime`);

-- templet_group 表索引优化
CREATE INDEX `idx_templet_group_user_deleted_created` ON `templet_group`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_group_delete_time` ON `templet_group`(`deleteTime`);

-- cloudfile 表索引优化
CREATE INDEX `idx_cloudfile_user_deleted_created` ON `cloudfile`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_cloudfile_delete_time` ON `cloudfile`(`deleteTime`);

-- =====================================================
-- 第三部分：创建数据恢复日志表
-- =====================================================

CREATE TABLE `data_recovery_logs` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `userId` INT NOT NULL COMMENT '用户ID',
  `dataType` VARCHAR(50) NOT NULL COMMENT '数据类型：template, template_group, cloudfile',
  `targetId` INT NOT NULL COMMENT '目标数据ID',
  `targetName` VARCHAR(255) COMMENT '目标数据名称（冗余字段，便于查询）',
  `recoveryType` VARCHAR(50) NOT NULL COMMENT '恢复类型：single, batch, batch_by_date',
  `recoveryStatus` VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '恢复状态：success, failed',
  `errorMessage` TEXT COMMENT '错误信息（恢复失败时）',
  `recoveryTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '恢复时间',
  `originalDeleteTime` DATETIME COMMENT '原始删除时间',
  `metadata` JSON COMMENT '额外元数据（JSON格式）',
  INDEX `idx_user_recovery_time` (`userId`, `recoveryTime`),
  INDEX `idx_data_type_target` (`dataType`, `targetId`),
  INDEX `idx_recovery_time` (`recoveryTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据恢复操作日志';

-- =====================================================
-- 第四部分：创建归档记录表
-- =====================================================

CREATE TABLE `archive_records` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `dataType` VARCHAR(50) NOT NULL COMMENT '数据类型：template, template_group, cloudfile',
  `originalId` INT NOT NULL COMMENT '原始数据ID',
  `userId` INT NOT NULL COMMENT '用户ID',
  `archiveKey` VARCHAR(500) NOT NULL COMMENT '归档存储键（OSS路径）',
  `archiveSize` BIGINT COMMENT '归档文件大小（字节）',
  `archiveTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '归档时间',
  `deleteTime` DATETIME NOT NULL COMMENT '原始删除时间',
  `metadata` JSON COMMENT '原始数据的元信息',
  `status` VARCHAR(20) NOT NULL DEFAULT 'archived' COMMENT '状态：archived, restored, expired',
  INDEX `idx_data_type_original` (`dataType`, `originalId`),
  INDEX `idx_user_archive_time` (`userId`, `archiveTime`),
  INDEX `idx_archive_time` (`archiveTime`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据归档记录';

-- =====================================================
-- 第五部分：验证脚本
-- =====================================================

-- 验证字段是否添加成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('templet', 'templet_group', 'cloudfile')
  AND COLUMN_NAME = 'deleteTime'
ORDER BY TABLE_NAME;

-- 验证新表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('data_recovery_logs', 'archive_records');

-- 验证索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('templet', 'templet_group', 'cloudfile', 'data_recovery_logs', 'archive_records')
  AND INDEX_NAME LIKE '%delete%' OR INDEX_NAME LIKE '%recovery%' OR INDEX_NAME LIKE '%archive%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- 执行说明
-- =====================================================

/*
执行步骤：
1. 备份数据库
2. 在测试环境先执行此脚本
3. 验证现有功能正常工作
4. 重新生成Model类
5. 在生产环境执行

回滚方案（如需要）：
ALTER TABLE `templet` DROP COLUMN `deleteTime`;
ALTER TABLE `templet_group` DROP COLUMN `deleteTime`;
ALTER TABLE `cloudfile` DROP COLUMN `deleteTime`;
DROP TABLE `data_recovery_logs`;
DROP TABLE `archive_records`;

注意事项：
- 此脚本为增量更新，不会影响现有数据
- 新增字段默认为NULL，表示数据未删除
- 建议在业务低峰期执行
- 执行前请确保数据库备份完成
*/
