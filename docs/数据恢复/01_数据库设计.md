# XPrinter数据恢复功能 - 数据库设计文档

## 1. 设计概述

### 1.1 设计原则
- **软删除机制**：所有可恢复数据采用软删除，添加 `deleteTime` 字段
- **保持兼容性**：基于现有表结构进行扩展，不破坏现有功能
- **命名一致性**：遵循项目现有的 `createTime`、`updateTime` 驼峰命名风格
- **简化权限**：暂不实现VIP权限验证，所有用户都可访问数据恢复功能
- **云端存储**：去掉本地存储，专注于云端热存储和冷存储

### 1.2 涉及的数据类型
- **用户模板** (`templet` 表)：用户创建的自定义模板
- **模板分组** (`templet_group` 表)：用户创建的模板分组
- **云端文件** (`cloudfile` 表)：用户上传的文件资源

### 1.3 数据库表概览

| 表名 | 类型 | 用途 | 关键字段 |
|------|------|------|----------|
| `templet` | 现有表扩展 | 用户模板 | `deleteTime` |
| `templet_group` | 现有表扩展 | 模板分组 | `deleteTime` |
| `cloudfile` | 现有表扩展 | 云端文件 | `deleteTime` |
| `data_recovery_logs` | 新增表 | 恢复记录 | `recoveryType`, `targetId` |
| `archive_records` | 新增表 | 归档记录 | `archiveKey`, `originalId` |

## 2. 现有表结构分析

### 2.1 templet 表（用户模板）
**当前字段结构**：
```sql
-- 基于 BaseTemplet.java 分析的字段
id                  INT PRIMARY KEY AUTO_INCREMENT
userId              INT NOT NULL                    -- 用户ID
groupId             INT                             -- 分组ID
name                VARCHAR(255)                    -- 模板名称
cover               VARCHAR(500)                    -- 封面图片URL
gap                 FLOAT                           -- 间隙
height              INT                             -- 高度
width               INT                             -- 宽度
paperType           INT                             -- 纸张类型
printDirection      INT                             -- 打印方向
data                TEXT                            -- 模板数据
blackLabelGap       FLOAT                           -- 黑标间隙
blackLabelOffset    FLOAT                           -- 黑标偏移
createTime          DATETIME                        -- 创建时间
updateTime          DATETIME                        -- 更新时间
type                INT                             -- 模板类型
nameEn              VARCHAR(255)                    -- 英文名称
nameKor             VARCHAR(255)                    -- 韩文名称
machineType         INT                             -- 机器类型
cutAfterPrint       INT                             -- 打印后切纸
labelNum            INT                             -- 标签数量
labelGap            FLOAT                           -- 标签间隙
shareUser           INT                             -- 分享用户
multiLabelType      INT                             -- 多标签类型
paperTearType       INT                             -- 撕纸类型
labelType           INT                             -- 标签类型
eancode             VARCHAR(255)                    -- 条码
```

### 2.2 templet_group 表（模板分组）
**当前字段结构**：
```sql
-- 基于现有代码分析
id                  INT PRIMARY KEY AUTO_INCREMENT
userId              INT NOT NULL                    -- 用户ID
name                VARCHAR(255)                    -- 分组名称
createTime          DATETIME                        -- 创建时间
updateTime          DATETIME                        -- 更新时间
type                INT                             -- 分组类型
```

### 2.3 cloudfile 表（云端文件）
**当前字段结构**：
```sql
-- 基于 BaseCloudfile.java 分析的字段
id                  INT PRIMARY KEY AUTO_INCREMENT
userId              INT NOT NULL                    -- 用户ID
name                VARCHAR(255)                    -- 文件名称
url                 VARCHAR(500)                    -- 文件URL
type                INT                             -- 文件类型
createTime          DATETIME                        -- 创建时间
```

## 3. 软删除字段扩展

### 3.1 需要添加的软删除字段

为支持数据恢复功能，需要为以下表添加软删除字段：

```sql
-- 为 templet 表添加软删除字段
ALTER TABLE `templet`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';

-- 为 templet_group 表添加软删除字段
ALTER TABLE `templet_group`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';

-- 为 cloudfile 表添加软删除字段
ALTER TABLE `cloudfile`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';
```

### 3.2 软删除字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `deleteTime` | DATETIME NULL | 删除时间，NULL表示未删除，有值表示已删除 |

**注意**：
1. 使用 `deleteTime` 而不是 `deleted_at`，保持与项目现有 `createTime`、`updateTime` 命名风格一致
2. 不需要 `deleted_by` 字段，因为用户只能删除自己的数据，属于冗余信息

## 4. 新增数据恢复相关表

### 4.1 data_recovery_logs 表（数据恢复日志）

```sql
CREATE TABLE `data_recovery_logs` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `userId` INT NOT NULL COMMENT '用户ID',
  `dataType` VARCHAR(50) NOT NULL COMMENT '数据类型：template, template_group, cloudfile',
  `targetId` INT NOT NULL COMMENT '目标数据ID',
  `targetName` VARCHAR(255) COMMENT '目标数据名称（冗余字段，便于查询）',
  `recoveryType` VARCHAR(50) NOT NULL COMMENT '恢复类型：single, batch, batch_by_date',
  `recoveryStatus` VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '恢复状态：success, failed',
  `errorMessage` TEXT COMMENT '错误信息（恢复失败时）',
  `recoveryTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '恢复时间',
  `originalDeleteTime` DATETIME COMMENT '原始删除时间',
  `metadata` JSON COMMENT '额外元数据（JSON格式）',
  INDEX `idx_user_recovery_time` (`userId`, `recoveryTime`),
  INDEX `idx_data_type_target` (`dataType`, `targetId`),
  INDEX `idx_recovery_time` (`recoveryTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据恢复操作日志';
```

### 4.2 archive_records 表（归档记录）

```sql
CREATE TABLE `archive_records` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `dataType` VARCHAR(50) NOT NULL COMMENT '数据类型：template, template_group, cloudfile',
  `originalId` INT NOT NULL COMMENT '原始数据ID',
  `userId` INT NOT NULL COMMENT '用户ID',
  `archiveKey` VARCHAR(500) NOT NULL COMMENT '归档存储键（OSS路径）',
  `archiveSize` BIGINT COMMENT '归档文件大小（字节）',
  `archiveTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '归档时间',
  `deleteTime` DATETIME NOT NULL COMMENT '原始删除时间',
  `metadata` JSON COMMENT '原始数据的元信息',
  `status` VARCHAR(20) NOT NULL DEFAULT 'archived' COMMENT '状态：archived, restored, expired',
  INDEX `idx_data_type_original` (`dataType`, `originalId`),
  INDEX `idx_user_archive_time` (`userId`, `archiveTime`),
  INDEX `idx_archive_time` (`archiveTime`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据归档记录';
```

## 5. 索引优化设计

### 5.1 软删除查询优化索引

```sql
-- templet 表索引优化
CREATE INDEX `idx_templet_user_deleted_created` ON `templet`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_deleted_range` ON `templet`(`deleteTime`, `userId`);
CREATE INDEX `idx_templet_deleted_at` ON `templet`(`deleteTime`);

-- templet_group 表索引优化
CREATE INDEX `idx_templet_group_user_deleted_created` ON `templet_group`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_group_deleted_at` ON `templet_group`(`deleteTime`);

-- cloudfile 表索引优化
CREATE INDEX `idx_cloudfile_user_deleted_created` ON `cloudfile`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_cloudfile_deleted_at` ON `cloudfile`(`deleteTime`);
```

### 5.2 索引设计说明

| 索引名 | 用途 | 查询场景 |
|--------|------|----------|
| `idx_*_user_deleted_created` | 用户已删除数据查询 | 按用户查询已删除数据，按创建时间排序 |
| `idx_*_deleted_range` | 时间范围查询 | 按删除时间范围查询数据 |
| `idx_*_deleted_at` | 删除状态查询 | 区分已删除和未删除数据 |

**注意**：虽然索引名称保留了原始的命名格式（如 `idx_*_deleted_at`），但实际字段名已更新为驼峰命名法（如 `deleteTime`）。

## 6. 数据生命周期管理

### 6.1 数据状态流转

```
正常数据 (deleteTime = NULL)
    ↓ 软删除
热存储已删除数据 (deleteTime != NULL, < 6个月)
    ↓ 归档 (定时任务)
冷存储归档数据 (archive_records表 + OSS存储, 6个月-1年)
    ↓ 清理 (定时任务)
永久删除 (1年后)
```

### 6.2 存储策略

| 时间范围 | 存储位置 | 查询性能 | 成本 |
|----------|----------|----------|------|
| 0-6个月 | MySQL热存储 | 高 | 中 |
| 6个月-1年 | 阿里云OSS冷归档 | 低 | 低 |
| 1年后 | 永久删除 | - | - |

## 7. 数据迁移脚本

### 7.1 添加软删除字段的迁移脚本

```sql
-- 迁移脚本：V2__add_soft_delete_fields.sql

-- 为 templet 表添加软删除字段
ALTER TABLE `templet`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';

-- 为 templet_group 表添加软删除字段
ALTER TABLE `templet_group`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';

-- 为 cloudfile 表添加软删除字段
ALTER TABLE `cloudfile`
ADD COLUMN `deleteTime` DATETIME NULL DEFAULT NULL COMMENT '删除时间';

-- 创建索引
CREATE INDEX `idx_templet_user_deleted_created` ON `templet`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_deleted_at` ON `templet`(`deleteTime`);

CREATE INDEX `idx_templet_group_user_deleted_created` ON `templet_group`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_templet_group_deleted_at` ON `templet_group`(`deleteTime`);

CREATE INDEX `idx_cloudfile_user_deleted_created` ON `cloudfile`(`userId`, `deleteTime`, `createTime`);
CREATE INDEX `idx_cloudfile_deleted_at` ON `cloudfile`(`deleteTime`);
```

## 8. 查询示例

### 8.1 常用查询SQL

```sql
-- 查询用户的已删除模板（6个月内）
SELECT * FROM templet
WHERE userId = ?
  AND deleteTime IS NOT NULL
  AND deleteTime > DATE_SUB(NOW(), INTERVAL 6 MONTH)
ORDER BY deleteTime DESC;

-- 查询指定时间范围内删除的数据
SELECT * FROM templet
WHERE userId = ?
  AND deleteTime BETWEEN ? AND ?
ORDER BY deleteTime DESC;

-- 查询正常（未删除）的数据
SELECT * FROM templet
WHERE userId = ?
  AND deleteTime IS NULL
ORDER BY createTime DESC;
```

---

**文档版本**: V1.0  
**创建时间**: 2024-07-18  
**负责人**: 后端开发团队  
**审核状态**: 待审核
