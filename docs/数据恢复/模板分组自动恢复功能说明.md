# 模板分组智能调整功能说明

## 功能概述

在数据恢复功能中，当用户恢复某个模板时，系统会自动检查该模板所在的分组状态。如果分组已被删除（软删除或不存在），系统会将模板的 `groupId` 调整为 -1（未分组），确保模板恢复后能正常显示在"未分组"列表中。

## 功能特性

### 1. 自动检测分组状态
- 当恢复模板时，系统会检查模板的 `groupId` 字段
- 如果 `groupId` 为空或为 -1，表示模板没有分组，跳过分组检查
- 如果 `groupId` 有效，系统会查询对应的分组信息

### 2. 智能分组调整
- 检查分组是否存在
- 检查分组是否属于当前用户
- 检查分组是否被软删除（`deleteTime` 不为 NULL）
- 如果分组不存在、无权限或被删除，将模板的 `groupId` 设为 -1

### 3. 简化的处理逻辑
- 不尝试恢复已删除的分组，避免复杂的权限和时间验证
- 直接将模板调整为"未分组"状态，确保模板能正常显示
- 减少恢复操作的复杂性和失败风险

### 4. 详细的调整日志
- 记录分组调整的原因（不存在、无权限、已删除）
- 在恢复消息中提示用户分组状态变化
- 便于用户了解模板分组的调整情况

## 使用场景

### 场景1：分组被软删除
1. 用户删除了一个分组，该分组被软删除
2. 用户想要恢复该分组下的某个模板
3. 系统检测到分组已被删除，将模板调整为"未分组"
4. 恢复后模板显示在"未分组"列表中，用户可以重新分组

### 场景2：分组不存在
1. 分组可能因为数据清理或其他原因被永久删除
2. 用户尝试恢复关联该分组的模板
3. 系统检测到分组不存在，将模板调整为"未分组"
4. 模板成功恢复，避免因分组缺失导致的显示问题

### 场景3：分组权限问题
1. 模板关联的分组不属于当前用户（可能是数据异常）
2. 系统检测到权限问题，将模板调整为"未分组"
3. 确保用户只能看到自己的数据，避免权限泄露

## API 接口变化

### 恢复单个数据接口
**接口路径**: `POST /api/v2/datarecovery/recoverSingleData`

**返回值变化**:
```json
{
  "success": true,
  "msg": "数据恢复成功",
  "code": "200"
}
```

如果分组被调整，返回消息会包含调整信息：
```json
{
  "success": true,
  "msg": "数据恢复成功",
  "code": "200"
}
```

注意：目标名称会包含分组调整信息，如：
- "我的模板（原分组已不存在，已调整为未分组）"
- "我的模板（原分组\"工作模板\"已被删除，已调整为未分组）"
- "我的模板（原分组无权限访问，已调整为未分组）"

### 批量恢复接口
**接口路径**: `POST /api/v2/datarecovery/recoverBatchData`

批量恢复模板时，每个模板都会进行分组检查，可能会调整多个模板的分组状态。

## 数据库变化

### 模板数据调整
- 恢复模板时，如果分组有问题，会直接修改模板的 `groupId` 字段为 -1
- 不会产生额外的数据库记录，只是调整现有模板数据

### 日志记录
- 分组调整的详细信息会记录在应用日志中
- 恢复操作的日志记录保持不变，只是目标名称可能包含调整信息

## 错误处理

### 可能的错误情况
1. **分组检查异常**: 数据库查询异常等技术问题
2. **模板更新失败**: 调整 groupId 后保存失败

### 错误处理策略
- 分组状态问题（不存在、无权限、已删除）不会阻止模板恢复，只会调整分组
- 只有技术异常才会阻止恢复操作
- 确保模板能够成功恢复，即使分组有问题
- 详细的调整日志记录，便于用户了解变化

## 性能考虑

### 查询优化
- 利用现有的数据库索引进行分组查询
- 单次额外查询，对性能影响最小

### 事务处理
- 分组调整和模板恢复在同一个事务中执行
- 确保操作的原子性，模板数据保持一致

## 兼容性说明

### 向后兼容
- 现有的恢复接口保持不变
- 只是增强了恢复逻辑，不影响现有功能
- 对于没有分组的模板，行为完全不变

### 前端适配
- 前端无需修改，接口返回格式保持兼容
- 可以通过返回消息中的调整信息提示用户
- 用户可以在恢复后重新为模板分组

## 测试建议

### 测试用例
1. 恢复有分组的模板（分组正常存在）
2. 恢复有分组的模板（分组已被软删除）
3. 恢复有分组的模板（分组不存在）
4. 恢复有分组的模板（分组不属于当前用户）
5. 恢复无分组的模板（groupId 为 -1 或 null）
6. 批量恢复包含不同分组状态的模板

### 验证要点
- 分组状态是否正确检测
- groupId 是否正确调整为 -1
- 调整信息是否正确显示
- 模板是否成功恢复
- 恢复后模板是否显示在"未分组"中
