alter table xinye_app_bak.font add column `fontGermanyName` varchar(50) NOT NULL COMMENT '德语'
alter table xinye_app_bak.font add column `fontItalyName` varchar(50) NOT NULL COMMENT '意大利语'
alter table xinye_app_bak.font add column `fontSpainName` varchar(50) NOT NULL COMMENT '西班牙语'
alter table xinye_app_bak.font add column `fontFranceName` varchar(50) NOT NULL COMMENT '法语'
alter table xinye_app_bak.font add column `fontValue` varchar(50) NOT NULL COMMENT 'value' 
alter table xinye_app_bak.font add column `fontCover` varchar(500) NOT NULL COMMENT '封面'

CREATE TABLE `xinye_app_bak`.`font_kind_lang` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '字体类型',
  `fontKind` varchar(32) NOT NULL COMMENT '字体类型',
  `fontLang` varchar(32) DEFAULT NULL COMMENT '所属语言',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_fontKind` (`fontKind`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='字体类型语言对照表';


INSERT INTO `xinye_app_bak`.`font_kind_lang` (`id`, `fontKind`, `fontLang`) VALUES ('1', '简体', 'zh_cn');
INSERT INTO `xinye_app_bak`.`font_kind_lang` (`id`, `fontKind`, `fontLang`) VALUES ('2', 'English', 'en');
INSERT INTO `xinye_app_bak`.`font_kind_lang` (`id`, `fontKind`, `fontLang`) VALUES ('3', '繁体', 'zh_hk');
INSERT INTO `xinye_app_bak`.`font_kind_lang` (`id`, `fontKind`, `fontLang`) VALUES ('4', '日本語', 'Japanese');
INSERT INTO `xinye_app_bak`.`font_kind_lang` (`id`, `fontKind`, `fontLang`) VALUES ('5', '한국어', 'korea');



DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '1');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '4');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '5');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '7');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '8');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '13');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '14');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '15');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '16');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '17');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '18');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '19');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '20');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '21');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '22');
DELETE FROM `xinye_app_bak`.`font` WHERE (`fontId` = '23');


//从4barcode 复制过来 可能要根据实际调整位置
insert into xinye_app_bak.font  select fontId, fontName, fontEnglishName, fontTraditionalName, fontKoreanName, fontKind, fontUrl, sysUserId, createTime, fontGermanyName, fontItalyName, fontSpainName, fontFranceName,fontValue,fontCover from  barcode4_test.font

//修复fontValue 是链接的问题

UPDATE `xinye_app_bak`.`font` a  SET `fontValue` = a.fontName
UPDATE `xinye_app_bak`.`font` SET `fontValue` = '迷你简丫丫' WHERE (`fontId` = '5');
UPDATE `xinye_app_bak`.`font` SET `fontValue` = 'YouYuan' WHERE (`fontId` = '8');
UPDATE `xinye_app_bak`.`font` SET `fontValue` = 'Cute Font' WHERE (`fontId` = '17');
UPDATE `xinye_app_bak`.`font` SET `fontValue` = 'Jua' WHERE (`fontId` = '18');