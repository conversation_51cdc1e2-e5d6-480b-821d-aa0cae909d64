-- XPrinter打印记录功能 - 数据库迁移脚本 V1.0
-- 创建时间: 2024-07-28
-- 说明: 为打印记录功能创建相关数据库表

use xinye_app_v3;

-- =====================================================
-- 创建打印记录表
-- =====================================================

CREATE TABLE `print_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `print_type` tinyint(4) NOT NULL COMMENT '打印类型：1-模板打印，2-文档打印，3-图片打印',
  `source_id` int(11) DEFAULT NULL COMMENT '源数据ID（模板ID、文档ID、图片ID等）',
  `source_name` varchar(255) NOT NULL COMMENT '源数据名称（模板名称、文档名称、图片名称）',
  `source_cover` varchar(500) DEFAULT NULL COMMENT '预览图URL',
  `print_width` int(11) DEFAULT NULL COMMENT '打印宽度（mm）',
  `print_height` int(11) DEFAULT NULL COMMENT '打印高度（mm）',
  `print_copies` int(11) NOT NULL DEFAULT '1' COMMENT '打印份数',
  `print_platform` tinyint(4) NOT NULL COMMENT '打印端：1-手机iOS，2-手机安卓，3-电脑Windows，4-电脑Mac',
  `print_time` datetime NOT NULL COMMENT '打印时间',
  `source_data` text COMMENT '源数据内容（JSON格式，用于重新打印）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型（PDF、JPG、PNG等）',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `print_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '打印状态：1-成功，2-失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_print_type` (`print_type`),
  KEY `idx_print_time` (`print_time`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_user_type_time` (`user_id`, `print_type`, `print_time`),
  KEY `idx_source_name` (`source_name`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打印记录表';

-- =====================================================
-- 插入测试数据（可选，用于开发测试）
-- =====================================================

-- 注意：以下测试数据仅用于开发环境测试，生产环境请删除
-- INSERT INTO `print_record` (`user_id`, `print_type`, `source_id`, `source_name`, `source_cover`, `print_width`, `print_height`, `print_copies`, `print_platform`, `print_time`, `source_data`, `print_status`) VALUES
-- (1, 1, 123, '商品标签模板', 'http://example.com/cover1.jpg', 50, 30, 5, 1, NOW(), '{"templateData": "test"}', 1),
-- (1, 1, 124, '价格标签模板', 'http://example.com/cover2.jpg', 40, 25, 3, 2, NOW(), '{"templateData": "test"}', 1),
-- (1, 1, 125, '地址标签模板', 'http://example.com/cover3.jpg', 60, 35, 2, 3, NOW(), '{"templateData": "test"}', 1);

-- =====================================================
-- 验证表创建
-- =====================================================

-- 查看表结构
-- DESC print_record;

-- 查看索引
-- SHOW INDEX FROM print_record;

-- 查看表状态
-- SHOW TABLE STATUS LIKE 'print_record';
