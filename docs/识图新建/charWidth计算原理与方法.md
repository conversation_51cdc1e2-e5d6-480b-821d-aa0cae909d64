# charWidth 计算原理与方法逻辑

## 概述

在识图新建功能中，`charWidth`（字符宽度）是一个重要的文本样式属性，用于计算平均字符宽度以便在模板系统中准确还原文本排版。本文档详细分析其计算原理和实现方法。

## 核心设计思路

### 1. 数据来源
- **主要数据源**: TextIn OCR API 返回的 `char_positions` 字段
- **数据格式**: 每个字符的四角坐标数组 `[x1, y1, x2, y2, x3, y3, x4, y4]`
- **降级策略**: 当 `char_positions` 不可用时，使用简单的 `文本宽度 / 字符数` 算法

### 2. 智能计算策略
系统采用字符分类的智能策略，按字符类型分别统计宽度，优先使用更稳定的字符类型作为基准：

**优先级顺序:**
1. **中文字符** - 最稳定（等宽字体特性）
2. **数字字符** - 次稳定（通常等宽）
3. **英文字符** - 较稳定
4. **标点符号** - 最不稳定

## 核心代码架构

### 主要类和方法

#### 1. CoordinateUtils.calculateSmartCharWidth()
**位置**: `src/main/java/com/sandu/xinye/api/v2/ocr/util/CoordinateUtils.java:134-189`

**核心算法流程**:
```java
public static double calculateSmartCharWidth(String text, List<List<Integer>> charPositions) {
    // 1. 数据验证
    // 2. 字符分类统计
    // 3. 按类型计算平均宽度
    // 4. 智能策略选择最优结果
    // 5. 精度控制（保留1位小数）
}
```

#### 2. ElementConverter.calculateCharWidthFromContent()
**位置**: `src/main/java/com/sandu/xinye/api/v2/ocr/ElementConverter.java:1642-1652`

**调用流程**:
```java
private double calculateCharWidthFromContent(Map<String, Object> contentItem, String text, int width) {
    // 1. 尝试获取 char_positions
    // 2. 优先调用智能算法
    // 3. 降级到简单算法
}
```

## 详细算法分析

### 1. 字符分类逻辑

#### 字符类型判断方法
```java
// 中文字符 (Unicode范围)
private static boolean isChinese(char ch) {
    return (ch >= 0x4E00 && ch <= 0x9FFF) ||     // CJK统一汉字
           (ch >= 0x3400 && ch <= 0x4DBF) ||     // CJK扩展A
           (ch >= 0x20000 && ch <= 0x2A6DF) ||   // CJK扩展B
           // ... 更多CJK扩展区域
}

// 英文字母
private static boolean isEnglish(char ch) {
    return (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z');
}

// 数字
private static boolean isDigit(char ch) {
    return ch >= '0' && ch <= '9';
}
```

### 2. 单字符宽度计算

#### 基于四角坐标的宽度计算
```java
private static double calculateSingleCharWidth(List<Integer> charPosition) {
    // 提取四个X坐标: [x1, y1, x2, y2, x3, y3, x4, y4]
    int x1 = charPosition.get(0), x2 = charPosition.get(2);
    int x3 = charPosition.get(4), x4 = charPosition.get(6);
    
    // 计算最小和最大X坐标
    int minX = Math.min(Math.min(x1, x2), Math.min(x3, x4));
    int maxX = Math.max(Math.max(x1, x2), Math.max(x3, x4));
    
    return maxX - minX; // 字符实际宽度
}
```

### 3. 智能策略选择

#### 优先级选择逻辑
```java
private static double selectOptimalCharWidth(
    int chineseCount, double chineseWidthSum,
    int englishCount, double englishWidthSum,
    int digitCount, double digitWidthSum,
    int punctuationCount, double punctuationWidthSum) {
    
    // 策略1：存在中文字符，以中文为准（最稳定）
    if (chineseCount > 0) {
        return chineseWidthSum / chineseCount;
    }
    
    // 策略2：数字占多数且数量 >= 英文，以数字为准
    if (digitCount > 0 && digitCount >= englishCount) {
        return digitWidthSum / digitCount;
    }
    
    // 策略3：英文占多数，以英文为准
    if (englishCount > 0) {
        return englishWidthSum / englishCount;
    }
    
    // 策略4：只有标点符号，以标点为准
    if (punctuationCount > 0) {
        return punctuationWidthSum / punctuationCount;
    }
    
    return 0.0; // 默认返回
}
```

## 算法优势分析

### 1. 准确性提升
- **几何精确性**: 基于每个字符的实际四角坐标，而非整体文本区域平均值
- **字符类型适配**: 针对不同字符类型的字体特性采用差异化策略
- **中文优先**: 中文字符通常等宽且稳定，作为最可靠的基准

### 2. 鲁棒性保障
- **多层降级**: char_positions → 简单算法 → 默认值
- **异常处理**: 完整的数据验证和错误处理机制
- **精度控制**: 保留1位小数避免过度精确

### 3. 性能优化
- **单次遍历**: 一次循环完成所有字符的分类和统计
- **早期过滤**: 跳过无效字符宽度，避免干扰结果

## 实际应用场景

### 1. 文本元素处理
**调用位置**: `ElementConverter.java:189` 和 `ElementConverter.java:397-398`
```java
// 在文本元素转换时调用
double charWidth = calculateCharWidthFromContent(contentItem, text, width);
response.addTextElement(minX, minY, width, height, text, isBold, isItalic, charWidth, ...);
```

### 2. 表格单元格处理  
**调用位置**: `ElementConverter.java:2053-2056`
```java
// 表格单元格也需要计算字符宽度
List<List<Integer>> charPositions = (List<List<Integer>>) contentItem.get("char_positions");
double charWidth = (charPositions != null && !charPositions.isEmpty()) ?
    CoordinateUtils.calculateSmartCharWidth(cellText, charPositions) :
    (cellText.length() > 0 ? (double) cellWidth / cellText.length() : 0.0);
```

### 3. 字体大小推算
**工具方法**: `CoordinateUtils.calculateFontSize()`
```java
// 基于字符宽度推算字体大小
public static String calculateFontSize(double charWidthPx) {
    // 经验公式：字体大小 ≈ 字符宽度 * 0.75
    double fontSize = charWidthPx * 0.75;
    return DECIMAL_FORMAT.format(fontSize);
}
```

## 测试验证

### 测试用例覆盖
1. **TableCellStyleTest.java**: 验证表格单元格的 charWidth 计算
2. **ComprehensiveOcrTest.java**: 综合测试包含 charWidth 的完整OCR流程
3. **NewArchitectureOcrTest.java**: 新架构下的 charWidth 验证

### 预期结果范围
- **合理区间**: 5.0 - 50.0 像素
- **中文字符**: 通常 12-24 像素（根据字体大小）
- **英文数字**: 通常 6-16 像素（根据字体大小）

## 配置与调优

### 关键参数
- **精度控制**: `Math.round(result * 10.0) / 10.0` - 保留1位小数
- **异常阈值**: 跳过宽度 <= 0 的无效字符
- **字体推算系数**: 0.75 (字体大小 = charWidth * 0.75)

### 日志输出
系统提供详细的计算日志便于调试：
```java
LogKit.info("智能计算charWidth: " + smartCharWidth + " for text: " + text);
LogKit.info("使用简单算法计算charWidth: " + smartCharWidth + " for text: " + text);
```

## 总结

charWidth 计算采用了基于 TextIn OCR 数据的智能分析算法，通过字符分类、几何精确计算和优先级策略选择，实现了高精度的字符宽度推算。该算法在识图新建功能中发挥关键作用，确保文本元素能够准确还原到模板系统中。

**核心特点:**
- ✅ 基于真实几何数据，精度高
- ✅ 字符类型智能分类，适应性强  
- ✅ 多层降级策略，鲁棒性好
- ✅ 完整的测试验证覆盖
- ✅ 详细的日志便于调试

---
*文档更新时间: 2025-08-08*
*相关代码版本: v5.4.0*