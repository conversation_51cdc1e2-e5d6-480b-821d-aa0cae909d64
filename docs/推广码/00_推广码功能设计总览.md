# 推广码功能设计总览

## 1. 功能概述

推广码功能允许用户绑定代理商的推广码，支持代理商分佣体系。该功能采用简化设计，用户侧只负责存储推广码信息，所有代理商管理和分佣逻辑由第三方系统处理。

## 2. 核心需求

### 2.1 用户注册时绑定推广码
- 用户在注册时可以传入推广码参数
- 推广码绑定成功后，该用户的相关操作可触发代理商分佣

### 2.2 已注册用户绑定推广码  
- 为已注册用户提供推广码绑定接口
- 用户在个人中心可以绑定推广码
- 防止重复绑定，一个用户只能绑定一个推广码

### 2.3 分佣触发场景
- 用户注册后绑定推广码：代理商可获得注册分佣
- 绑定推广码的用户首次打印：代理商可获得首次打印分佣

## 3. 技术架构

### 3.1 系统边界
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   XPrinter      │    │   第三方分佣    │    │   代理商管理    │
│   用户系统      │◄──►│   系统          │◄──►│   系统          │
│                 │    │                 │    │                 │
│ - 用户注册      │    │ - 分佣计算      │    │ - 代理商信息    │
│ - 推广码绑定    │    │ - 分佣记录      │    │ - 推广码管理    │
│ - 用户操作记录  │    │ - 结算处理      │    │ - 业绩统计      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 数据流向
1. 用户注册/绑定推广码 → XPrinter系统存储
2. 用户操作触发 → 通知第三方分佣系统
3. 第三方系统计算分佣 → 记录到代理商账户

## 4. 实施范围

### 4.1 XPrinter系统负责
- ✅ 用户表扩展（添加推广码字段）
- ✅ 注册接口改造（支持推广码参数）
- ✅ 推广码绑定接口开发
- ✅ 用户操作事件通知（可选）

### 4.2 第三方系统负责
- ❌ 代理商信息管理
- ❌ 推广码生成和验证
- ❌ 分佣金额计算
- ❌ 分佣记录存储
- ❌ 代理商结算处理

## 5. 数据库设计

### 5.1 用户表扩展
```sql
ALTER TABLE `user` ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '绑定的推广码';
ALTER TABLE `user` ADD COLUMN `promotion_bind_time` DATETIME DEFAULT NULL COMMENT '推广码绑定时间';
```

### 5.2 字段说明
- `promotion_code`: 用户绑定的推广码，允许为空
- `promotion_bind_time`: 推广码绑定时间，用于统计和审计

## 6. 接口设计

### 6.1 注册接口改造
**现有接口**: `POST /api/register`
**新增参数**: `promotionCode` (可选)

### 6.2 推广码绑定接口
**新增接口**: `POST /api/user/bindPromotionCode`
**参数**: `promotionCode` (必填)

## 7. 业务规则

### 7.1 推广码格式
- 长度：6-8位字符
- 字符集：字母和数字组合
- 大小写不敏感

### 7.2 绑定规则
- 一个用户只能绑定一个推广码
- 绑定后不可修改
- 推广码有效性由第三方系统验证

### 7.3 分佣触发
- 注册分佣：用户注册时绑定推广码
- 首次打印分佣：绑定推广码的用户首次打印操作

## 8. 实施计划

### 8.1 开发阶段（预计1周）
- [x] 数据库表结构设计
- [ ] 用户表字段扩展
- [ ] 注册接口改造
- [ ] 推广码绑定接口开发

### 8.2 测试阶段（预计2天）
- [ ] 单元测试
- [ ] 接口测试
- [ ] 集成测试

### 8.3 部署阶段（预计1天）
- [ ] 数据库迁移脚本执行
- [ ] 代码部署
- [ ] 功能验证

## 9. 风险评估

### 9.1 技术风险
- **低风险**: 仅涉及用户表扩展和接口开发
- **数据一致性**: 使用事务确保推广码绑定的原子性

### 9.2 业务风险
- **推广码冲突**: 由第三方系统保证推广码唯一性
- **重复绑定**: 通过业务逻辑防止用户重复绑定

## 10. 后续扩展

### 10.1 可选功能
- 推广码绑定历史记录
- 用户推广统计信息
- 推广码有效期验证

### 10.2 集成接口
- 与第三方分佣系统的API对接
- 用户操作事件的实时通知
- 分佣数据的定期同步
