# 推广码功能数据库设计

## 1. 设计原则

### 1.1 简化原则
- 最小化数据库变更，只在用户表添加必要字段
- 避免创建额外的关联表，降低系统复杂度
- 推广码验证和管理逻辑由第三方系统处理

### 1.2 扩展性原则
- 字段设计考虑未来可能的扩展需求
- 保持与现有用户体系的兼容性
- 为后续与第三方系统集成预留接口

## 2. 用户表扩展

### 2.1 新增字段设计

```sql
-- 为用户表添加推广码相关字段
ALTER TABLE `user` ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '绑定的推广码';
ALTER TABLE `user` ADD COLUMN `promotion_bind_time` DATETIME DEFAULT NULL COMMENT '推广码绑定时间';

-- 添加索引以优化查询性能
CREATE INDEX `idx_promotion_code` ON `user` (`promotion_code`);
CREATE INDEX `idx_promotion_bind_time` ON `user` (`promotion_bind_time`);
```

### 2.2 字段详细说明

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 说明 |
|--------|------|------|--------|----------|------|
| `promotion_code` | VARCHAR | 50 | NULL | YES | 用户绑定的推广码 |
| `promotion_bind_time` | DATETIME | - | NULL | YES | 推广码绑定时间 |

### 2.3 字段设计考虑

**promotion_code 字段：**
- 长度设置为50字符，支持各种推广码格式
- 允许为空，表示用户未绑定推广码
- 不设置唯一约束，因为多个用户可以使用同一个推广码
- 添加索引以支持按推广码查询用户

**promotion_bind_time 字段：**
- 记录推广码绑定的具体时间
- 用于统计分析和审计追踪
- 允许为空，与promotion_code字段保持一致性

## 3. 数据完整性约束

### 3.1 业务约束
```sql
-- 业务规则：如果绑定了推广码，必须有绑定时间
-- 这个约束通过应用层逻辑实现，而不是数据库约束
-- 原因：保持数据库设计的简洁性
```

### 3.2 数据一致性
- 推广码绑定操作使用事务确保原子性
- 绑定时间与推广码同时更新，保证数据一致性
- 通过应用层逻辑防止重复绑定

## 4. 索引设计

### 4.1 查询优化索引
```sql
-- 推广码查询索引（支持按推广码查找用户）
CREATE INDEX `idx_promotion_code` ON `user` (`promotion_code`);

-- 绑定时间查询索引（支持按时间范围统计）
CREATE INDEX `idx_promotion_bind_time` ON `user` (`promotion_bind_time`);

-- 复合索引（支持推广码+时间的复合查询）
CREATE INDEX `idx_promotion_code_time` ON `user` (`promotion_code`, `promotion_bind_time`);
```

### 4.2 索引使用场景
- **按推广码查询用户列表**：用于统计某个推广码的用户数量
- **按时间范围查询绑定记录**：用于分析推广码绑定趋势
- **推广效果分析**：结合用户注册时间和绑定时间进行分析

## 5. 数据迁移脚本

### 5.1 完整迁移脚本
```sql
-- 推广码功能数据库迁移脚本
-- 执行时间：2024-07-30
-- 目的：为用户表添加推广码支持

-- 1. 添加推广码字段
ALTER TABLE `user` ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '绑定的推广码';

-- 2. 添加绑定时间字段  
ALTER TABLE `user` ADD COLUMN `promotion_bind_time` DATETIME DEFAULT NULL COMMENT '推广码绑定时间';

-- 3. 添加查询优化索引
CREATE INDEX `idx_promotion_code` ON `user` (`promotion_code`);
CREATE INDEX `idx_promotion_bind_time` ON `user` (`promotion_bind_time`);

-- 4. 验证字段添加成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND COLUMN_NAME IN ('promotion_code', 'promotion_bind_time');

-- 5. 验证索引创建成功
SHOW INDEX FROM `user` WHERE Key_name LIKE 'idx_promotion%';
```

### 5.2 回滚脚本
```sql
-- 推广码功能回滚脚本（如需要）
-- 注意：执行前请备份数据

-- 1. 删除索引
DROP INDEX `idx_promotion_code` ON `user`;
DROP INDEX `idx_promotion_bind_time` ON `user`;

-- 2. 删除字段
ALTER TABLE `user` DROP COLUMN `promotion_code`;
ALTER TABLE `user` DROP COLUMN `promotion_bind_time`;
```

## 6. 数据统计查询

### 6.1 常用统计SQL
```sql
-- 查询绑定推广码的用户总数
SELECT COUNT(*) as bound_users 
FROM user 
WHERE promotion_code IS NOT NULL;

-- 查询各推广码的用户数量
SELECT 
    promotion_code,
    COUNT(*) as user_count,
    MIN(promotion_bind_time) as first_bind_time,
    MAX(promotion_bind_time) as last_bind_time
FROM user 
WHERE promotion_code IS NOT NULL 
GROUP BY promotion_code 
ORDER BY user_count DESC;

-- 查询指定时间范围内的推广码绑定情况
SELECT 
    DATE(promotion_bind_time) as bind_date,
    COUNT(*) as daily_binds
FROM user 
WHERE promotion_bind_time BETWEEN '2024-07-01' AND '2024-07-31'
GROUP BY DATE(promotion_bind_time)
ORDER BY bind_date;

-- 查询某个推广码的用户列表
SELECT 
    userId,
    userNickName,
    userPhone,
    promotion_code,
    promotion_bind_time,
    createTime
FROM user 
WHERE promotion_code = 'ABC123'
ORDER BY promotion_bind_time DESC;
```

## 7. 性能考虑

### 7.1 查询性能
- 推广码字段添加索引，支持快速查询
- 绑定时间字段添加索引，支持时间范围查询
- 复合索引支持多条件查询优化

### 7.2 存储空间
- VARCHAR(50)字段占用空间较小
- 大部分用户可能不绑定推广码，NULL值不占用额外空间
- 索引空间开销可控

### 7.3 写入性能
- 新增字段不影响现有的用户注册性能
- 推广码绑定操作为低频操作，对系统性能影响很小

## 8. 数据安全

### 8.1 数据保护
- 推广码信息不包含敏感数据
- 绑定时间用于审计，需要保护数据完整性
- 遵循现有用户数据的安全策略

### 8.2 访问控制
- 推广码信息仅用户本人和管理员可查看
- 统计数据可提供给授权的分析人员
- 遵循数据最小化原则

## 9. 监控和维护

### 9.1 数据监控
- 监控推广码绑定的成功率
- 统计推广码使用情况
- 监控异常的推广码绑定行为

### 9.2 数据维护
- 定期清理无效的推广码数据（如果需要）
- 监控索引性能，必要时进行优化
- 定期备份推广码相关数据
