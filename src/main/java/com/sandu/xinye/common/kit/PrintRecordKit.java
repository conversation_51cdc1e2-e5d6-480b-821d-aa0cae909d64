package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.api.v2.printrecord.PrintRecordService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.model.PrintRecord;
import com.sandu.xinye.common.model.Templet;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * 打印记录工具类
 * 用于在各种打印操作中记录打印行为
 * 
 * <AUTHOR> Team
 */
public class PrintRecordKit {

    /**
     * 异步保存模板打印记录
     * 
     * @param userId 用户ID
     * @param templet 模板对象
     * @param printCopies 打印份数
     * @param printPlatform 打印端类型
     */
    public static void saveTemplatePrintRecordAsync(Integer userId, Templet templet, Integer printCopies, Integer printPlatform) {
        if (userId == null || templet == null) {
            LogKit.warn("保存模板打印记录失败：用户ID或模板对象为空");
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                saveTemplatePrintRecord(userId, templet, printCopies, printPlatform);
            } catch (Exception e) {
                LogKit.error("异步保存模板打印记录失败", e);
            }
        });
    }

    /**
     * 同步保存模板打印记录
     * 
     * @param userId 用户ID
     * @param templet 模板对象
     * @param printCopies 打印份数
     * @param printPlatform 打印端类型
     */
    public static void saveTemplatePrintRecord(Integer userId, Templet templet, Integer printCopies, Integer printPlatform) {
        try {
            PrintRecord record = new PrintRecord();
            record.setUserId(userId)
                  .setPrintType(Constant.PRINT_RECORD_TYPE_TEMPLATE)
                  .setSourceId(templet.getId())
                  .setSourceName(templet.getName())
                  .setSourceCover(templet.getCover())
                  .setPrintWidth(templet.getWidth())
                  .setPrintHeight(templet.getHeight())
                  .setPrintCopies(printCopies != null ? printCopies : 1)
                  .setPrintPlatform(printPlatform != null ? printPlatform : detectPlatform())
                  .setPrintTime(new Date())
                  .setSourceData(templet.getData())
                  .setPrintStatus(Constant.PRINT_STATUS_SUCCESS);

            boolean success = record.save();
            if (!success) {
                LogKit.warn("保存模板打印记录失败：数据库保存失败");
            }
        } catch (Exception e) {
            LogKit.error("保存模板打印记录失败", e);
        }
    }

    /**
     * 通过Service保存模板打印记录（推荐使用）
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param templateCover 模板封面
     * @param printWidth 打印宽度
     * @param printHeight 打印高度
     * @param printCopies 打印份数
     * @param printPlatform 打印端
     * @param templateData 模板数据
     */
    public static void saveTemplatePrintRecordViaService(Integer userId, Integer templateId, String templateName, 
                                                       String templateCover, Integer printWidth, Integer printHeight, 
                                                       Integer printCopies, Integer printPlatform, String templateData) {
        try {
            PrintRecordService.me.saveTemplatePrintRecord(userId, templateId, templateName, templateCover, 
                    printWidth, printHeight, printCopies, printPlatform, templateData);
        } catch (Exception e) {
            LogKit.error("通过Service保存模板打印记录失败", e);
        }
    }

    /**
     * 异步通过Service保存模板打印记录
     */
    public static void saveTemplatePrintRecordViaServiceAsync(Integer userId, Integer templateId, String templateName, 
                                                            String templateCover, Integer printWidth, Integer printHeight, 
                                                            Integer printCopies, Integer printPlatform, String templateData) {
        CompletableFuture.runAsync(() -> {
            saveTemplatePrintRecordViaService(userId, templateId, templateName, templateCover, 
                    printWidth, printHeight, printCopies, printPlatform, templateData);
        });
    }

    /**
     * 检测打印端类型（简单实现，可根据实际需求扩展）
     * 
     * @return 打印端类型
     */
    private static Integer detectPlatform() {
        // 这里可以根据User-Agent或其他信息检测平台
        // 暂时返回默认值
        return Constant.PRINT_PLATFORM_WINDOWS;
    }

    /**
     * 根据User-Agent检测打印端类型
     * 
     * @param userAgent 用户代理字符串
     * @return 打印端类型
     */
    public static Integer detectPlatformFromUserAgent(String userAgent) {
        if (StrKit.isBlank(userAgent)) {
            return Constant.PRINT_PLATFORM_WINDOWS;
        }

        userAgent = userAgent.toLowerCase();
        
        if (userAgent.contains("iphone") || userAgent.contains("ipad")) {
            return Constant.PRINT_PLATFORM_IOS;
        } else if (userAgent.contains("android")) {
            return Constant.PRINT_PLATFORM_ANDROID;
        } else if (userAgent.contains("mac")) {
            return Constant.PRINT_PLATFORM_MAC;
        } else if (userAgent.contains("windows")) {
            return Constant.PRINT_PLATFORM_WINDOWS;
        }
        
        return Constant.PRINT_PLATFORM_WINDOWS; // 默认值
    }

    /**
     * 获取打印端类型名称
     * 
     * @param platform 打印端类型
     * @return 打印端类型名称
     */
    public static String getPlatformName(Integer platform) {
        if (platform == null) {
            return "未知";
        }
        
        switch (platform) {
            case Constant.PRINT_PLATFORM_IOS:
                return "手机iOS";
            case Constant.PRINT_PLATFORM_ANDROID:
                return "手机安卓";
            case Constant.PRINT_PLATFORM_WINDOWS:
                return "电脑Windows";
            case Constant.PRINT_PLATFORM_MAC:
                return "电脑Mac";
            default:
                return "未知";
        }
    }

    /**
     * 验证打印端类型是否有效
     * 
     * @param platform 打印端类型
     * @return true-有效，false-无效
     */
    public static boolean isValidPlatform(Integer platform) {
        return platform != null && 
               (platform == Constant.PRINT_PLATFORM_IOS ||
                platform == Constant.PRINT_PLATFORM_ANDROID ||
                platform == Constant.PRINT_PLATFORM_WINDOWS ||
                platform == Constant.PRINT_PLATFORM_MAC);
    }

    /**
     * 记录打印失败
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param errorMessage 错误信息
     * @param printPlatform 打印端
     */
    public static void recordPrintFailure(Integer userId, Integer templateId, String templateName, 
                                        String errorMessage, Integer printPlatform) {
        try {
            PrintRecord record = new PrintRecord();
            record.setUserId(userId)
                  .setPrintType(Constant.PRINT_RECORD_TYPE_TEMPLATE)
                  .setSourceId(templateId)
                  .setSourceName(templateName)
                  .setPrintPlatform(printPlatform != null ? printPlatform : detectPlatform())
                  .setPrintTime(new Date())
                  .setPrintStatus(Constant.PRINT_STATUS_FAILED)
                  .setErrorMessage(errorMessage);

            record.save();
        } catch (Exception e) {
            LogKit.error("记录打印失败信息失败", e);
        }
    }
}
