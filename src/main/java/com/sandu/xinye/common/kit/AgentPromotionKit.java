package com.sandu.xinye.common.kit;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 代理商推广码验证工具类
 * 用于调用代理商系统接口验证推广码是否存在
 * 
 * <AUTHOR> Team
 * @since 2024-08-06
 */
public class AgentPromotionKit {
    
    /**
     * 签名密钥
     */
    private static final String SIGN_SECRET = "123!@#wqee";
    
    /**
     * 获取代理商验证接口URL
     */
    private static String getVerifyUrl() {
        return PropKit.get("promotion.agent.verify.url", "https://console-api.xpyun.net/api/admin/member/verifyPromotion");
    }
    
    /**
     * 获取请求超时时间
     */
    private static int getTimeout() {
        return PropKit.getInt("promotion.agent.verify.timeout", 5000);
    }
    
    /**
     * 是否启用代理商验证
     */
    public static boolean isVerifyEnabled() {
        return PropKit.getBoolean("promotion.agent.verify.enable", true);
    }
    
    /**
     * 验证失败时是否允许绑定
     */
    public static boolean isFailoverEnabled() {
        return PropKit.getBoolean("promotion.agent.verify.failover", true);
    }
    
    /**
     * 验证推广码是否在代理商系统中存在
     * 
     * @param userId 用户ID
     * @param promotionCode 推广码
     * @return 验证结果，true-存在，false-不存在，null-验证失败或功能未启用
     */
    public static Boolean verifyPromotionCode(Integer userId, String promotionCode) {
        // 检查是否启用代理商验证
        if (!isVerifyEnabled()) {
            LogKit.info("代理商推广码验证功能已禁用 - userId: " + userId + ", promotionCode: " + promotionCode);
            return null;
        }
        
        if (userId == null || StrKit.isBlank(promotionCode)) {
            LogKit.warn("代理商推广码验证参数无效 - userId: " + userId + ", promotionCode: " + promotionCode);
            return null;
        }
        
        try {
            // 构建请求参数
            long timestamp = System.currentTimeMillis();
            String sign = generateSign(userId.toString(), timestamp);
            
            Map<String, Object> params = new HashMap<>();
            params.put("user", userId.toString());
            params.put("timestamp", String.valueOf(timestamp));
            params.put("sign", sign);
            params.put("promotionCode", promotionCode);
            
            // 发送HTTP请求
            String responseJson = sendHttpRequest(params);
            if (StrKit.isBlank(responseJson)) {
                LogKit.error("代理商推广码验证失败 - 响应为空");
                return null;
            }
            
            // 解析响应结果
            return parseResponse(responseJson, userId, promotionCode);
            
        } catch (Exception e) {
            LogKit.error("代理商推广码验证异常 - userId: " + userId + ", promotionCode: " + promotionCode, e);
            return null;
        }
    }
    
    /**
     * 生成签名
     * 签名规则：SHA1(user + '123!@#wqee' + timestamp)
     * 
     * @param user 用户标识
     * @param timestamp 时间戳
     * @return SHA1签名字符串
     */
    private static String generateSign(String user, long timestamp) {
        String data = user + SIGN_SECRET + timestamp;
        return HashKit.sha1(data);
    }
    
    /**
     * 发送HTTP请求
     * 
     * @param params 请求参数
     * @return 响应内容
     */
    private static String sendHttpRequest(Map<String, Object> params) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            // 创建HTTP客户端
            httpClient = HttpClients.createDefault();
            
            // 创建POST请求
            HttpPost httpPost = new HttpPost(getVerifyUrl());
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            
            // 设置超时配置
            int timeout = getTimeout();
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(timeout)
                    .setConnectTimeout(timeout)
                    .setConnectionRequestTimeout(timeout)
                    .build();
            httpPost.setConfig(requestConfig);
            
            // 设置请求体
            String jsonParams = JSON.toJSONString(params);
            StringEntity entity = new StringEntity(jsonParams, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            
            LogKit.info("代理商推广码验证请求 - URL: " + getVerifyUrl() + ", params: " + jsonParams);
            
            // 执行请求
            response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            
            if (responseEntity != null) {
                String responseContent = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                LogKit.info("代理商推广码验证响应 - " + responseContent);
                return responseContent;
            }
            
        } catch (Exception e) {
            LogKit.error("代理商推广码验证HTTP请求异常", e);
        } finally {
            // 关闭资源
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                LogKit.error("关闭HTTP连接异常", e);
            }
        }
        
        return null;
    }
    
    /**
     * 解析响应结果
     * 
     * @param responseJson 响应JSON字符串
     * @param userId 用户ID（用于日志）
     * @param promotionCode 推广码（用于日志）
     * @return 验证结果
     */
    private static Boolean parseResponse(String responseJson, Integer userId, String promotionCode) {
        try {
            JSONObject jsonObject = JSON.parseObject(responseJson);
            
            // 检查状态码
            Integer status = jsonObject.getInteger("status");
            if (status == null || status != 200) {
                LogKit.warn("代理商推广码验证状态异常 - userId: " + userId + 
                           ", promotionCode: " + promotionCode + ", status: " + status);
                return null;
            }
            
            // 获取验证结果
            Boolean data = jsonObject.getBoolean("data");
            Boolean rel = jsonObject.getBoolean("rel");
            
            if (data != null && data.equals(rel)) {
                if (data) {
                    LogKit.info("代理商推广码验证成功 - userId: " + userId + ", promotionCode: " + promotionCode);
                    return true;
                } else {
                    String message = jsonObject.getString("message");
                    LogKit.info("代理商推广码验证失败 - userId: " + userId + 
                               ", promotionCode: " + promotionCode + ", message: " + message);
                    return false;
                }
            } else {
                LogKit.warn("代理商推广码验证响应格式异常 - userId: " + userId + 
                           ", promotionCode: " + promotionCode + ", data: " + data + ", rel: " + rel);
                return null;
            }
            
        } catch (Exception e) {
            LogKit.error("代理商推广码验证响应解析异常 - userId: " + userId + 
                        ", promotionCode: " + promotionCode + ", response: " + responseJson, e);
            return null;
        }
    }
    
    /**
     * 检查代理商验证功能是否可用
     * 可以在系统初始化时调用此方法进行健康检查
     * 
     * @return true-可用，false-不可用
     */
    public static boolean isAgentVerifyAvailable() {
        try {
            // 使用测试参数验证接口可用性
            Boolean result = verifyPromotionCode(999999, "TEST");
            // 只要能得到响应（不管是true还是false）就认为接口可用
            return result != null;
        } catch (Exception e) {
            LogKit.error("代理商验证接口健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取签名算法描述（用于调试和文档）
     * 
     * @return 签名算法说明
     */
    public static String getSignAlgorithmDescription() {
        return "SHA1(user + '123!@#wqee' + timestamp)";
    }
}