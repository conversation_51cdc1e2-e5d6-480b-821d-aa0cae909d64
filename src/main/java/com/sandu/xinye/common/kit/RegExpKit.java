package com.sandu.xinye.common.kit;

import org.apache.log4j.Logger;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @ClassName: RegExpKit
 * @Description: (正则)
 */

public class RegExpKit {

    private static final Logger logger = Logger.getLogger(RegExpKit.class);

    /**
     * 匹配重名文件夹
     * <p>
     * <p>
     * 格式: 未命名(1)
     * <p>
     */
    public static final String GROUP_RENAME_REGEXP = "^\\S*\\((\\d+)\\)$";

    /**
     * 匹配重名Cloudfile
     * <p>
     * <p>
     * 格式: 未命名(1)
     * <p>
     */
    public static final String CLOUDFILE_RENAME_REGEXP = "^\\S*(\\((\\d+)\\))$";

    /**
     * 匹配默认模板名称重名
     * <p>
     * <p>
     * 格式: label(1)
     * <p>
     */
    public static final String TEMPLET_RENAME_REGEXP = "^label\\((\\d+)\\)$";

    /**
     * 模板复制重名判断规则
     * <p>
     * <p>
     * 格式: 模板(1)
     * <p>
     */
    public static final String TEMPLET_COPY_RENAME_REGEXP = "^\\S*(\\((\\d+)\\))$";

    /**
     * 用户模板名称重命名匹配
     */
    public static final String TEMPLET_USERDEF_RENAME_REGEXP = "^\\S*_副本\\((\\d+)\\)$";

    /**
     * 匹配邮箱
     */
    public static final String EMAIL_REGEXP = "^/^\\w+([\\.\\-]\\w+)*\\@\\w+([\\.\\-]\\w+)*\\.\\w+$/$";

    /**
     * 大小写敏感的正规表达式批配
     *
     * @param source 批配的源字符串
     * @param regexp 批配的正规表达式
     * @return 如果源字符串符合要求返回真, 否则返回假
     */
    public static Matcher matchRegExp(String source, String regexp) {
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(regexp);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(source);
        if (m.find()) {
            return m;
        } else {
            return null;
        }
    }
    public static Boolean match(String source, String regexp) {
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(regexp);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(source);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static String replaceRegExp(String str, String regex, String replaceStr) {

        String afterReplace = str.replaceAll(regex, replaceStr);

        return afterReplace;
    }
}
