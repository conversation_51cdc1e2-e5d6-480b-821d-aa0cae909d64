package com.sandu.xinye.common.kit;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;


/**
* @ClassName: DateKit
* @Description: ( 日期工具类)
*/
    
public class MyDateKit {
	
	public static final String[] zodiacArr = { "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊" };
	 
	public static final String[] constellationArr = { "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座" };
	 
	public static final int[] constellationEdgeDay = { 20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22 };
	 
	/**
	 * 根据日期获取生肖
	 * @return
	 */
	public static String getZodica(Date date) {
	    Calendar cal = Calendar.getInstance();
	    cal.setTime(date);
	    return zodiacArr[cal.get(Calendar.YEAR) % 12];
	}
	 
	/**
	 * 根据日期获取星座
	 * @return
	 */
	public static String getConstellation(Date date) {
	    if (date == null) {
	        return "";
	    }
	    Calendar cal = Calendar.getInstance();
	    cal.setTime(date);
	    int month = cal.get(Calendar.MONTH);
	    int day = cal.get(Calendar.DAY_OF_MONTH);
	    if (day < constellationEdgeDay[month]) {
	        month = month - 1;
	    }
	    if (month >= 0) {
	        return constellationArr[month];
	    }
	    // default to return 魔羯
	    return constellationArr[11];
	}
	
    
    /**
     * 获取当天的开始时间
     * @return
     */
    public static java.util.Date getDayBegin() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    
    /**
     * 获取当天的结束时间
     * @return
     */
    public static java.util.Date getDayEnd() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }
    
    /**
     * 获取昨天的开始时间
     * @return
     */
    public static Date getBeginDayOfYesterday() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayBegin());
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }
    
    /**
     * 获取昨天的结束时间
     * @return
     */
    public static Date getEndDayOfYesterDay() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayEnd());
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }
    
    /**
     * 获取明天的开始时间
     * @return
     */
    public static Date getBeginDayOfTomorrow() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayBegin());
        cal.add(Calendar.DAY_OF_MONTH, 1);

        return cal.getTime();
    }
    
    /**
     * 获取明天的结束时间
     * @return
     */
    public static Date getEndDayOfTomorrow() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayEnd());
        cal.add(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }
    
    /**
     * 获取本周的开始时间
     * @return
     */
    public static Date getBeginDayOfWeek() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayofweek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayofweek == 1) {
            dayofweek += 7;
        }
        cal.add(Calendar.DATE, 2 - dayofweek);
        return getDayStartTime(cal.getTime());
    }
    
    /**
     * 获取本周的结束时间
     * @return
     */
    public static Date getEndDayOfWeek(){
        Calendar cal = Calendar.getInstance();
        cal.setTime(getBeginDayOfWeek());  
        cal.add(Calendar.DAY_OF_WEEK, 6); 
        Date weekEndSta = cal.getTime();
        return getDayEndTime(weekEndSta);
    }
    
    /**
     * 获取本月的开始时间
     * @return
     */
     public static Date getBeginDayOfMonth() {
            Calendar calendar = Calendar.getInstance();
            calendar.set(getNowYear(), getNowMonth() - 1, 1);
            return getDayStartTime(calendar.getTime());
        }
     
    /**
     * 获取本月的结束时间
     * @return
     */
     public static Date getEndDayOfMonth() {
            Calendar calendar = Calendar.getInstance();
            calendar.set(getNowYear(), getNowMonth() - 1, 1);
            int day = calendar.getActualMaximum(5);
            calendar.set(getNowYear(), getNowMonth() - 1, day);
            return getDayEndTime(calendar.getTime());
        }
     
     /**
      * 获取本年的开始时间
      * @return
      */
     public static java.util.Date getBeginDayOfYear() {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, getNowYear());
            // cal.set
            cal.set(Calendar.MONTH, Calendar.JANUARY);
            cal.set(Calendar.DATE, 1);

            return getDayStartTime(cal.getTime());
        }
     
     /**
      * 获取本年的结束时间
      * @return
      */
     public static java.util.Date getEndDayOfYear() {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, getNowYear());
            cal.set(Calendar.MONTH, Calendar.DECEMBER);
            cal.set(Calendar.DATE, 31);
            return getDayEndTime(cal.getTime());
        }
     
    /**
     * 获取某个日期的开始时间
     * @param d
     * @return
     */
    public static Timestamp getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if(null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),    calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }
    
    /**
     * 获取某个日期的结束时间
     * @param d
     * @return
     */
    public static Timestamp getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if(null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),    calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return new Timestamp(calendar.getTimeInMillis());
    }
    
    /**
     * 获取某年某月的第一天
     * @param year
     * @param month
     * @return
     */
    public static Date getStartMonthDate(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getTime();
    }
    
    /**
     * 获取某年某月的最后一天
     * @param year
     * @param month
     * @return
     */
    public static Date getEndMonthDate(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        int day = calendar.getActualMaximum(5);
        calendar.set(year, month - 1, day);
        return calendar.getTime();
    }
    
    /**
     * 获取今年是哪一年
     * @return
     */
     public static Integer getNowYear() {
             Date date = new Date();
            GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
            gc.setTime(date);
            return Integer.valueOf(gc.get(1));
        }
     
     /**
      * 获取本月是哪一月
      * @return
      */
     public static int getNowMonth() {
             Date date = new Date();
            GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
            gc.setTime(date);
            return gc.get(2) + 1;
        }
     
     /**
      * 两个日期相减得到的天数
      * @param beginDate
      * @param endDate
      * @return
      */
     public static int getDiffDays(Date beginDate, Date endDate) {

            if (beginDate == null || endDate == null) {
                throw new IllegalArgumentException("getDiffDays param is null!");
            }

            long diff = (endDate.getTime() - beginDate.getTime())
                    / (1000 * 60 * 60 * 24);

            int days = new Long(diff).intValue();

            return days;
        }
     
    /**
     * 两个日期相减得到的毫秒数
     * @param beginDate
     * @param endDate
     * @return
     */
     public static long dateDiff(Date beginDate, Date endDate) {
            long date1ms = beginDate.getTime();
            long date2ms = endDate.getTime();
            return date2ms - date1ms;
        }
     
     /**
      * 获取两个日期中的最大日期
      * @param beginDate
      * @param endDate
      * @return
      */
     public static Date max(Date beginDate, Date endDate) {
            if (beginDate == null) {
                return endDate;
            }
            if (endDate == null) {
                return beginDate;
            }
            if (beginDate.after(endDate)) {
                return beginDate;
            }
            return endDate;
        }
     
     /**
      * 获取两个日期中的最小日期
      * @param beginDate
      * @param endDate
      * @return
      */
     public static Date min(Date beginDate, Date endDate) {
            if (beginDate == null) {
                return endDate;
            }
            if (endDate == null) {
                return beginDate;
            }
            if (beginDate.after(endDate)) {
                return endDate;
            }
            return beginDate;
        }
     
     /**
      * 返回某月该季度的第一个月
      * @param date
      * @return
      */
     public static Date getFirstSeasonDate(Date date) {
             final int[] SEASON = { 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4 };
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int sean = SEASON[cal.get(Calendar.MONTH)];
            cal.set(Calendar.MONTH, sean * 3 - 3);
            return cal.getTime();
        }
     
     /**
      * 返回某个日期下几天的日期
      * @param date
      * @param i
      * @return
      */
     public static Date getNextDay(Date date, int i) {
            Calendar cal = new GregorianCalendar();
            cal.setTime(date);
            cal.set(Calendar.DATE, cal.get(Calendar.DATE) + i);
            return cal.getTime();
        }
     
     /**
      * 返回某个日期前几天的日期
      * @param date
      * @param i
      * @return
      */
     public static Date getFrontDay(Date date, int i) {
            Calendar cal = new GregorianCalendar();
            cal.setTime(date);
            cal.set(Calendar.DATE, cal.get(Calendar.DATE) - i);
            return cal.getTime();
        }
     
     /**
      * 获取某年某月到某年某月按天的切片日期集合（间隔天数的日期集合）
      * @param beginYear
      * @param beginMonth
      * @param endYear
      * @param endMonth
      * @param k
      * @return
      */
     public static List<List<Date>> getTimeList(int beginYear, int beginMonth, int endYear,
                int endMonth, int k) {
            List<List<Date>> list = new ArrayList<List<Date>>();
            if (beginYear == endYear) {
                for (int j = beginMonth; j <= endMonth; j++) {
                    list.add(getTimeList(beginYear, j, k));

                }
            } else {
                {
                    for (int j = beginMonth; j < 12; j++) {
                        list.add(getTimeList(beginYear, j, k));
                    }

                    for (int i = beginYear + 1; i < endYear; i++) {
                        for (int j = 0; j < 12; j++) {
                            list.add(getTimeList(i, j, k));
                        }
                    }
                    for (int j = 0; j <= endMonth; j++) {
                        list.add(getTimeList(endYear, j, k));
                    }
                }
            }
            return list;
        }
     
     /**
      * 获取某年某月按天切片日期集合（某个月间隔多少天的日期集合）
      * @param beginYear
      * @param beginMonth
      * @param k
      * @return
      */
     public static List<Date> getTimeList(int beginYear, int beginMonth, int k) {
            List<Date> list = new ArrayList<Date>();
            Calendar begincal = new GregorianCalendar(beginYear, beginMonth, 1);
            int max = begincal.getActualMaximum(Calendar.DATE);
            for (int i = 1; i < max; i = i + k) {
                list.add(begincal.getTime());
                begincal.add(Calendar.DATE, k);
            }
            begincal = new GregorianCalendar(beginYear, beginMonth, max);
            list.add(begincal.getTime());
            return list;
        }
     
     /**
      * @Title: getMonths
      * @Description:  获得两个日期中间间隔的月份数
      * @param start
      * @param end
      * @return
      * @date 2019年1月30日  下午10:05:03
      * <AUTHOR>
      */
     public static int getMonths(Date start, Date end) {
 		if (start.after(end)) {
 			Date t = start;
 			start = end;
 			end = t;
 		}
 		Calendar startCalendar = Calendar.getInstance();
 		startCalendar.setTime(start);
 		Calendar endCalendar = Calendar.getInstance();
 		endCalendar.setTime(end);
 		Calendar temp = Calendar.getInstance();
 		temp.setTime(end);
 		temp.add(Calendar.DATE, 1);
  
 		int year = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
 		int month = endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
  
 		if ((startCalendar.get(Calendar.DATE) == 1)&& (temp.get(Calendar.DATE) == 1)) {
 			return year * 12 + month + 1;
 		} else if ((startCalendar.get(Calendar.DATE) != 1) && (temp.get(Calendar.DATE) == 1)) {
 			return year * 12 + month;
 		} else if ((startCalendar.get(Calendar.DATE) == 1) && (temp.get(Calendar.DATE) != 1)) {
 			return year * 12 + month;
 		} else {
 			return (year * 12 + month - 1) < 0 ? 0 : (year * 12 + month);
 		}
 	}
     
     @SuppressWarnings("unused")
	public static void main(String args[]){
    	 Date date = new Date();
    	 System.out.println(getEndMonthDate(2017, 5));
     }
  

}
