package com.sandu.xinye.common.kit;

import java.io.File;
import java.io.IOException;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.constant.Constant;
import com.xiaoleilu.hutool.io.FileUtil;

public class UploadFileMoveKit {

	/**
	 * @param fileUrl
	 *            temp目录文件url
	 * @param moveToFoloder
	 *            要移动的文件夹
	 * @return 返回相对路径 /upload/folder.../xxx.jpg
	 */
	public static String move(String fileUrl, String moveToFoloder) {
		if (fileUrl == null) {
			return null;
		}
		if (fileUrl.contains("temp")) {
			String fileName = fileUrl.substring(fileUrl.lastIndexOf("/"), fileUrl.length());
			String filePath = Constant.REAL_UPLOAD_PATH + "/temp/" + fileName;
			String newFilePath = Constant.REAL_UPLOAD_PATH + "/" + moveToFoloder + "/" + fileName;
			File file = new File(filePath);
			try {
				FileUtil.move(file, new File(newFilePath), true);
			} catch (IOException e) {
				LogKit.error(e.toString(), e);
			}
			return "/upload/" + moveToFoloder + fileName;
		}
		return fileUrl;
	}

}
