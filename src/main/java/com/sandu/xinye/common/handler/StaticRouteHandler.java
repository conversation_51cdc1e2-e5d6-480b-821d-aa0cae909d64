package com.sandu.xinye.common.handler;

import com.jfinal.handler.Handler;
import com.jfinal.kit.HandlerKit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: Wangpf
 * @description: 处理静态匹配
 * @date: 2022/6/10 16:08
 * @version: 1.0
 */
public class StaticRouteHandler  extends Handler{
    private String prefix;
    private Boolean replacePrefix;
    private String targetServerName;
    private int serverNameLength;

    public StaticRouteHandler(String prefix, String targetServerName) {
        this(prefix, targetServerName, true);
    }

    public StaticRouteHandler(String prefix, String targetServerName, Boolean replacePrefix) {
        this.prefix = prefix;
        this.targetServerName = targetServerName;
        this.format();
        this.serverNameLength = this.prefix.length();
        this.replacePrefix = replacePrefix;
    }

    private final void format() {
        if (this.prefix.endsWith("/")) {
            this.prefix = this.prefix.substring(0, this.prefix.length() - 1);
        }

        if (this.targetServerName.endsWith("/")) {
            this.targetServerName = this.targetServerName.substring(0, this.targetServerName.length() - 1);
        }

        if (this.targetServerName.indexOf("://") == -1) {
            this.targetServerName = "http://" + this.targetServerName;
        }

    }

    @Override
    public void handle(String target, HttpServletRequest request, HttpServletResponse response, boolean[] isHandled) {
        String url = request.getRequestURI();

        if (url.startsWith(this.prefix)) {
            isHandled[0] = true;
            String queryString = request.getQueryString();
            queryString = queryString == null ? "" : "?" + queryString;
            url = this.targetServerName + (replacePrefix ? url.substring(this.serverNameLength) : url) + queryString;

            response.setStatus(301);
            response.setHeader("Location", url);
            response.setHeader("Connection", "close");
        } else {
            this.next.handle(target, request, response, isHandled);
        }

    }

}
