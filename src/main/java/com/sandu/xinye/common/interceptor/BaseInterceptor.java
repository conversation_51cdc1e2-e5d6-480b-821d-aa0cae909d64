package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import org.apache.log4j.Logger;

/**
 * 防止攻击
 * 请求带上签名
 */
public class BaseInterceptor implements Interceptor {
	private static final Logger logger = Logger.getLogger(BaseInterceptor.class);
	private static Logger timeAround = Logger.getLogger("timeAround");

	@Override
	public void intercept(Invocation inv) {
		/**
		 * --功能耗时开启
		 */
		// 开始时间
		long start = System.currentTimeMillis();
		// java类名: 如 net.jfinal.Action.LoginAction
		String className = inv.getController().getClass().getName();
		// 方法名: 如 login
		String methodName = inv.getMethodName();

//		timeAround.info("into action " + className + "." + methodName);
//		// 获取所有参数
//		Map<String, String[]> parameterMap = inv.getController().getRequest().getParameterMap();
//		// 标记第几个参数
//		int index = 1;
//		// 遍历参数打印到日志
//		for (String key : parameterMap.keySet()) {
//			String[] params = parameterMap.get(key);
//			for (String param : params) {
//				timeAround.info(className + "." + methodName + "\t第" + index + "个参数:" + param);
//				index++;
//			}
//		}
		/**
		 * --若Controller层未捕获异常打印到error日志中
		 */
		try {
			inv.invoke();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("BaseInterceptor err", e);
		}
		//********************捕获异常结束********************\\
		timeAround.info(String.format("%s|#|%s|#|%s(ms)", className, methodName, (System.currentTimeMillis() - start)));
		//--------------------功能耗时结束--------------------\\
	}

}
