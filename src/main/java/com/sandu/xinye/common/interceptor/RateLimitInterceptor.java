package com.sandu.xinye.common.interceptor;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.IpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.IpFilter;
import org.apache.log4j.Logger;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 防止攻击
 * 同一IP限流
 *
 * <AUTHOR>
 */
public class RateLimitInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(AttackAntiInterceptor.class);

    /**
     * 每分钟最大访问次数
     */
    private static Integer MAX_ACCESS_PER_MINUTE = 10;
    Prop p = PropKit.use("common_config.txt");

    {
        String fileName = p.get("sqlConfig");
        p.append(fileName);
        MAX_ACCESS_PER_MINUTE = p.getInt("maxAccessPerMinute");
    }

    /**
     * 根据IP分不同的令牌桶, 每小时自动清理缓存
     */
    private static LoadingCache<String, AtomicLong> cachesForMinute = CacheBuilder.newBuilder()
            .maximumSize(1000L)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, AtomicLong>() {
                @Override
                public AtomicLong load(String key) {
                    return new AtomicLong(0);
                }
            });


    @Override
    public void intercept(Invocation inv) {
        Controller con = inv.getController();
        String ip = IpKit.getRealIp(con.getRequest());
        if (StrKit.isBlank(ip)) {
            con.renderJson(RetKit.fail(400, "参数错误！"));
            return;
        }

        try {
            // 检查每小时的限流
            AtomicLong limiter = cachesForMinute.get(ip);
            // IP是否在白名单中
            Boolean inWhiteList = isInWhiteList(ip);
            Boolean inBlackList = isInBlackList(ip);
            if (inBlackList) {
                con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "请求受限，请联系管理员！"));
                return;
            }
            if (!inWhiteList && limiter.incrementAndGet() > MAX_ACCESS_PER_MINUTE) {
                logger.info(String.format("当前IP[%s]请求太频繁，超出每分钟限制次数: %s", ip, MAX_ACCESS_PER_MINUTE));
                con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "请求太频繁，请稍后再试！"));
                return;
            }
            // TODO. 引入redis黑名单模式，超过一定次数加入黑名单

        } catch (ExecutionException e) {
            con.renderJson(RetKit.fail(400, "参数错误，接口请求失败！"));
            return;
        } catch (Exception e) {
            e.printStackTrace();
            con.renderJson(RetKit.fail(500, "系统内部错误！"));
            return;
        }

        inv.invoke();
    }

    private Boolean isInWhiteList(String ip) {
        IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 0", ip);
        return filter != null;
    }

    private Boolean isInBlackList(String ip) {
        IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 1", ip);
        return filter != null;
    }

}
