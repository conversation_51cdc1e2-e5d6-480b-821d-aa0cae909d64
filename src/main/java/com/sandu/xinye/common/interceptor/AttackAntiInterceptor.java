package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.CipherKit;
import com.sandu.xinye.common.kit.RetKit;
import org.apache.log4j.Logger;

import java.util.Map;
import java.util.TreeMap;

/**
 * 防止攻击
 * 请求带上签名
 * <AUTHOR>
 */
public class AttackAntiInterceptor implements Interceptor {
	private static final Logger logger = Logger.getLogger(AttackAntiInterceptor.class);

	/**
	 * 超时时间 300s
	 */
	private static final int EXPIRE_TIMES = 300000;

	@Override
	public void intercept(Invocation inv) {
		Controller con = inv.getController();
		String ip = con.getHeader("ip");
		String timestamp = con.getHeader("timestamp");
		String sign = con.getHeader("sign");

		Map<String, String> map = new TreeMap();
		map.put("ip", ip);
		map.put("timestamp", timestamp);

		logger.info("验证签名，请求参数：" + map.toString());

		if (StrKit.isBlank(ip) || StrKit.isBlank(timestamp) || StrKit.isBlank(sign)) {
			con.renderJson(RetKit.fail(RetConstant.CODE_ILLEGAL_REQ, "非法请求！"));
			return;
		}

		// 判断时间是否失效
		long currentTimeStamp = System.currentTimeMillis();
		long timeStampL = 0;
		try {
			timeStampL = Long.parseLong(timestamp);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}

		if (currentTimeStamp - timeStampL > EXPIRE_TIMES) {
			logger.info(String.format("参数时间戳非法，超出过期时间! - %s(ms)，当前时间戳: %s", (currentTimeStamp - timeStampL), currentTimeStamp));
			con.renderJson(RetKit.fail(RetConstant.CODE_ILLEGAL_REQ, "请求超时！"));
			return;
		}

		// 校验签名
		String calcSign = CipherKit.sign(map);
		if (!sign.equals(calcSign)) {
			logger.info("验证签名失败!");
			con.renderJson(RetKit.fail(RetConstant.CODE_ILLEGAL_REQ, "签名错误！"));
			return;
		}

		inv.invoke();

	}

}
