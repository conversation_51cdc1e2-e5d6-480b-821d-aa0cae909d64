package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.dto.DataRecoveryPermissionResult;
import com.sandu.xinye.common.enums.PermissionType;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.model.User;

import java.util.Calendar;
import java.util.Date;

/**
 * VIP权限服务
 * 提供用户权限检查和数据恢复时间限制的核心逻辑
 */
public class VipPermissionService {
    
    public static final VipPermissionService me = new VipPermissionService();
    
    /**
     * 检查用户是否有指定权限
     */
    public boolean hasPermission(Integer userId, PermissionType permissionType) {
        if (userId == null || permissionType == null) {
            return false;
        }
        
        try {
            User user = User.dao.findById(userId);
            if (user == null) {
                return false;
            }
            
            UserTier userTier = user.getUserTierEnum();
            
            // 根据权限类型和用户等级判断权限
            switch (permissionType) {
                case FEATURE_DATA_RECOVERY:
                    // 数据恢复功能：所有用户都有权限，但恢复期限不同
                    return true;
                    
                case FEATURE_OCR_CREATE:
                case FEATURE_TEAM_COLLABORATION:
                    // OCR和团队协作功能：仅VIP用户可用
                    return userTier.isVip();
                    
                case EXPERIENCE_AD_FREE:
                case EXPERIENCE_PRIORITY_SUPPORT:
                case EXPERIENCE_BETA_FEATURES:
                    // 体验增强功能：仅VIP用户可用
                    return userTier.isVip();
                    
                default:
                    return false;
            }
            
        } catch (Exception e) {
            LogKit.error("权限检查失败, userId: " + userId + ", permission: " + permissionType, e);
            return false; // 默认无权限，确保安全
        }
    }
    
    /**
     * 检查数据恢复权限
     */
    public DataRecoveryPermissionResult checkDataRecoveryPermission(Integer userId) {
        if (userId == null) {
            return DataRecoveryPermissionResult.denied("用户ID不能为空", false, null);
        }

        try {
            User user = User.dao.findById(userId);
            if (user == null) {
                return DataRecoveryPermissionResult.denied("用户不存在", false, null);
            }

            UserTier userTier = user.getUserTierEnum();
            Date earliestRecoveryDate = calculateEarliestRecoveryDate(userTier);
            int recoveryPeriodDays = calculateActualRecoveryPeriodDays(userTier);

            if (userTier.isVip()) {
                return DataRecoveryPermissionResult.vipUser(userTier, earliestRecoveryDate, recoveryPeriodDays);
            } else {
                return DataRecoveryPermissionResult.freeUser(earliestRecoveryDate, recoveryPeriodDays);
            }

        } catch (Exception e) {
            LogKit.error("检查数据恢复权限失败, userId: " + userId, e);
            return DataRecoveryPermissionResult.denied("系统错误", false, null);
        }
    }
    
    /**
     * 检查用户是否可以恢复指定时间的数据
     */
    public boolean canRecoverDataFromDate(Integer userId, Date deleteTime) {
        if (userId == null || deleteTime == null) {
            return false;
        }

        try {
            User user = User.dao.findById(userId);
            if (user == null) {
                return false;
            }

            UserTier userTier = user.getUserTierEnum();
            Date earliestRecoveryDate = calculateEarliestRecoveryDate(userTier);

            // 删除时间必须在可恢复期限内
            return deleteTime.after(earliestRecoveryDate) || deleteTime.equals(earliestRecoveryDate);

        } catch (Exception e) {
            LogKit.error("检查数据恢复时间权限失败, userId: " + userId + ", deleteTime: " + deleteTime, e);
            return false;
        }
    }
    
    /**
     * 获取用户的数据恢复期限（天数）
     */
    public int getUserDataRecoveryPeriodDays(Integer userId) {
        if (userId == null) {
            return 0;
        }

        try {
            User user = User.dao.findById(userId);
            if (user == null) {
                return 0;
            }

            UserTier userTier = user.getUserTierEnum();
            return calculateActualRecoveryPeriodDays(userTier);

        } catch (Exception e) {
            LogKit.error("获取用户数据恢复期限失败, userId: " + userId, e);
            return 0;
        }
    }
    
    /**
     * 计算最早可恢复的日期（基于用户等级）
     */
    private Date calculateEarliestRecoveryDate(UserTier userTier) {
        Calendar calendar = Calendar.getInstance();

        switch (userTier) {
            case FREE:
                calendar.add(Calendar.DAY_OF_MONTH, -7); // 免费用户7天
                break;
            case VIP_MONTHLY:
            case VIP_YEARLY:
            case VIP_LIFETIME:
                calendar.add(Calendar.MONTH, -6); // VIP用户真实的6个月
                break;
            default:
                calendar.add(Calendar.DAY_OF_MONTH, -7);
        }

        return calendar.getTime();
    }

    /**
     * 计算实际的恢复期限天数（用于显示）
     */
    private int calculateActualRecoveryPeriodDays(UserTier userTier) {
        Date earliestDate = calculateEarliestRecoveryDate(userTier);
        Date now = new Date();
        long diffInMillis = now.getTime() - earliestDate.getTime();
        return (int) (diffInMillis / (24 * 60 * 60 * 1000));
    }

    /**
     * 计算最早可恢复的日期（兼容旧方法）
     */
    private Date calculateEarliestRecoveryDate(int recoveryPeriodDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -recoveryPeriodDays);
        return calendar.getTime();
    }
    
    /**
     * 获取升级VIP的提示信息
     */
    public String getUpgradeMessage(UserTier currentTier) {
        if (currentTier.isVip()) {
            return null; // VIP用户不需要升级提示
        }
        
        return "升级VIP可恢复6个月内的数据，享受更多高级功能";
    }
}
