package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.model.ArchiveRecords;

import java.util.Date;
import java.util.List;

/**
 * 基于归档的数据生命周期管理器
 * 负责将软删除的数据归档，并最终从归档表中永久删除
 * 
 * 数据生命周期策略：
 * - 热存储期：软删除后6个月(180天)内保持在业务表中，可快速恢复
 * - 归档期：软删除6个月后迁移至归档表，释放业务表空间
 * - 永久删除：归档1年后(365天)永久删除，释放存储空间
 *
 * <AUTHOR> Team
 * @since 2024-07-25
 */
public class ArchiveBasedDataLifecycleManager implements DataLifecycleManager {

  // 支持的数据表
  private static final String[] SUPPORTED_TABLES = { "templet", "templet_group", "cloudfile" };

  // 软删除数据归档阈值（天）
  private final int archiveThresholdDays;

  // 归档数据永久删除阈值（天）
  private final int permanentDeleteThresholdDays;

  public ArchiveBasedDataLifecycleManager(int archiveThresholdDays, int permanentDeleteThresholdDays) {
    this.archiveThresholdDays = archiveThresholdDays;
    this.permanentDeleteThresholdDays = permanentDeleteThresholdDays;
  }

  @Override
  public void execute() {
    LogKit.info("开始执行数据生命周期管理任务...");
    archiveExpiredData();
    permanentlyDeleteArchivedData();
    LogKit.info("数据生命周期管理任务执行完毕。");
  }

  /**
   * 将已过期的软删除数据从业务表迁移到归档表
   */
  private void archiveExpiredData() {
    LogKit.info("开始归档已过期的软删除数据...");
    for (String tableName : SUPPORTED_TABLES) {
      try {
        int archivedCount = archiveDataForTable(tableName);
        if (archivedCount > 0) {
          LogKit.info(String.format("表 [%s] 成功归档 %d 条记录。", tableName, archivedCount));
        } else {
          LogKit.info(String.format("表 [%s] 没有需要归档的数据。", tableName));
        }
      } catch (Exception e) {
        LogKit.error(String.format("归档表 [%s] 的数据时发生错误: %s", tableName, e.getMessage()), e);
      }
    }
  }

  /**
   * 处理单个表的归档逻辑
   */
  private int archiveDataForTable(String tableName) {
    // 计算归档时间点
    Date archiveBeforeDate = new Date(System.currentTimeMillis() - (long) archiveThresholdDays * 24 * 60 * 60 * 1000);

    // 1. 查询需要归档的数据
    String selectSql = "SELECT * FROM " + tableName + " WHERE deleteTime IS NOT NULL AND deleteTime < ?";
    List<Record> expiredRecords = Db.find(selectSql, archiveBeforeDate);

    if (expiredRecords.isEmpty()) {
      return 0;
    }

    // 2. 开启事务
    int successCount = 0;
    for (Record record : expiredRecords) {
      // 3. 将数据插入归档表
      ArchiveRecords archive = new ArchiveRecords();
      archive.setOriginalTableName(tableName);
      archive.setOriginalRecordId(String.valueOf(record.getInt("id")));
      archive.setRecordContent(record.toJson()); // 将整条记录以JSON格式存储
      archive.setArchiveTime(new Date());
      archive.setOriginalDeleteTime(record.getDate("deleteTime"));
      if (archive.save()) {
        // 4. 从原表中删除已归档的数据
        int deleteResult = Db.update("DELETE FROM " + tableName + " WHERE id = ?", new Object[] { record.get("id") });
        if (deleteResult > 0) {
          successCount++;
        }
      }
    }
    return successCount;
  }

  /**
   * 从归档表中永久删除超过保留期限的数据
   */
  private void permanentlyDeleteArchivedData() {
    LogKit.info("开始永久删除已过期的归档数据...");

    // 计算永久删除的时间点
    Date deleteBeforeDate = new Date(
        System.currentTimeMillis() - (long) permanentDeleteThresholdDays * 24 * 60 * 60 * 1000);

    String deleteSql = "DELETE FROM archive_records WHERE archiveTime < ?";

    try {
      int deletedCount = Db.update(deleteSql, deleteBeforeDate);
      if (deletedCount > 0) {
        LogKit.info(String.format("成功从归档表中永久删除了 %d 条记录。", deletedCount));
      } else {
        LogKit.info("归档表中没有需要永久删除的数据。");
      }
    } catch (Exception e) {
      LogKit.error("从归档表中永久删除数据时发生错误: " + e.getMessage(), e);
    }
  }
}