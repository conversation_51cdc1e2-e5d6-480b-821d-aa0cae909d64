package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseHelp<M extends BaseHelp<M>> extends Model<M> implements IBean {

	public M setHelpId(java.lang.Integer helpId) {
		set("helpId", helpId);
		return (M)this;
	}
	
	public java.lang.Integer getHelpId() {
		return getInt("helpId");
	}

	public M setHelpVideo(java.lang.String helpVideo) {
		set("helpVideo", helpVideo);
		return (M)this;
	}
	
	public java.lang.String getHelpVideo() {
		return getStr("helpVideo");
	}

	public M setLink(java.lang.String link) {
		set("link", link);
		return (M)this;
	}

	public java.lang.String getLink() {
		return getStr("link");
	}

	public M setHelpAnswer(java.lang.String helpAnswer) {
		set("helpAnswer", helpAnswer);
		return (M)this;
	}

	public java.lang.String getHelpAnswer() {
		return getStr("helpAnswer");
	}

	public M setMachineId(java.lang.Integer machineId) {
		set("machineId", machineId);
		return (M)this;
	}
	
	public java.lang.Integer getMachineId() {
		return getInt("machineId");
	}

	public M setHelpKind(java.lang.Integer helpKind) {
		set("helpKind", helpKind);
		return (M)this;
	}
	
	public java.lang.Integer getHelpKind() {
		return getInt("helpKind");
	}

	public M setHelpLogo(java.lang.String helpLogo) {
		set("helpLogo", helpLogo);
		return (M)this;
	}
	
	public java.lang.String getHelpLogo() {
		return getStr("helpLogo");
	}

	public M setHelpName(java.lang.String helpName) {
		set("helpName", helpName);
		return (M)this;
	}
	
	public java.lang.String getHelpName() {
		return getStr("helpName");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
