package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseProductInfo<M extends BaseProductInfo<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setCode(java.lang.String code) {
		set("code", code);
		return (M)this;
	}
	
	public java.lang.String getCode() {
		return getStr("code");
	}

	public M setGoodsName(java.lang.String goodsName) {
		set("goods_name", goodsName);
		return (M)this;
	}
	
	public java.lang.String getGoodsName() {
		return getStr("goods_name");
	}

	public M setManuName(java.lang.String manuName) {
		set("manu_name", manuName);
		return (M)this;
	}
	
	public java.lang.String getManuName() {
		return getStr("manu_name");
	}

	public M setSpec(java.lang.String spec) {
		set("spec", spec);
		return (M)this;
	}
	
	public java.lang.String getSpec() {
		return getStr("spec");
	}

	public M setPrice(java.lang.String price) {
		set("price", price);
		return (M)this;
	}
	
	public java.lang.String getPrice() {
		return getStr("price");
	}

	public M setTrademark(java.lang.String trademark) {
		set("trademark", trademark);
		return (M)this;
	}
	
	public java.lang.String getTrademark() {
		return getStr("trademark");
	}

	public M setImg(java.lang.String img) {
		set("img", img);
		return (M)this;
	}
	
	public java.lang.String getImg() {
		return getStr("img");
	}

	public M setGoodsType(java.lang.String goodsType) {
		set("goods_type", goodsType);
		return (M)this;
	}
	
	public java.lang.String getGoodsType() {
		return getStr("goods_type");
	}

	public M setSptmImg(java.lang.String sptmImg) {
		set("sptm_img", sptmImg);
		return (M)this;
	}
	
	public java.lang.String getSptmImg() {
		return getStr("sptm_img");
	}

	public M setYcg(java.lang.String ycg) {
		set("ycg", ycg);
		return (M)this;
	}
	
	public java.lang.String getYcg() {
		return getStr("ycg");
	}

	public M setEngName(java.lang.String engName) {
		set("eng_name", engName);
		return (M)this;
	}
	
	public java.lang.String getEngName() {
		return getStr("eng_name");
	}

	public M setNote(java.lang.String note) {
		set("note", note);
		return (M)this;
	}
	
	public java.lang.String getNote() {
		return getStr("note");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
