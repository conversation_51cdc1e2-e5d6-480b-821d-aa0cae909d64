package com.sandu.xinye.common.model;

import com.jfinal.plugin.activerecord.ActiveRecordPlugin;

/**
 * Generated by JFinal, do not modify this file.
 * 
 * <pre>
 * Example:
 * public void configPlugin(Plugins me) {
 *     ActiveRecordPlugin arp = new ActiveRecordPlugin(...);
 *     _MappingKit.mapping(arp);
 *     me.add(arp);
 * }
 * </pre>
 */
public class _MappingKit {

	public static void mapping(ActiveRecordPlugin arp) {
		arp.addMapping("about", "aboutId", About.class);
		arp.addMapping("app_config", "config_key", AppConfig.class);
		arp.addMapping("archive_records", "id", ArchiveRecords.class);
		arp.addMapping("app_dict", "id", AppDict.class);
		arp.addMapping("app_version", "id", AppVersion.class);
		arp.addMapping("app_version_two", "id", AppVersionTwo.class);
		arp.addMapping("banner", "id", Banner.class);
		arp.addMapping("cloudfile", "id", Cloudfile.class);
		arp.addMapping("data_recovery_logs", "id", DataRecoveryLogs.class);
		arp.addMapping("download_set", "id", DownloadSet.class);
		arp.addMapping("everyday_data", "id", EverydayData.class);
		arp.addMapping("feedback", "feedbackId", Feedback.class);
		arp.addMapping("font", "fontId", Font.class);
		arp.addMapping("font_kind_lang", "id", FontKindLang.class);
		arp.addMapping("goods_templet", "id", GoodsTemplet.class);
		arp.addMapping("help", "helpId", Help.class);
		arp.addMapping("help_i18n", "id", HelpI18n.class);
		arp.addMapping("ip_filter", "ip", IpFilter.class);
		arp.addMapping("logo", "logoId", Logo.class);
		arp.addMapping("logo_child_kind", "logoChildKindId", LogoChildKind.class);
		arp.addMapping("logo_kind", "logoKindId", LogoKind.class);
		arp.addMapping("machine", "machineId", Machine.class);
		arp.addMapping("machine_i18n", "id", MachineI18n.class);
		arp.addMapping("menu", "menuId", Menu.class);
		arp.addMapping("operation_log", "id", OperationLog.class);
		arp.addMapping("print_record", "id", PrintRecord.class);
		arp.addMapping("product_info", "id", ProductInfo.class);
		arp.addMapping("survey", "id", Survey.class);
		arp.addMapping("survey_answer", "id", SurveyAnswer.class);
		arp.addMapping("survey_question", "id", SurveyQuestion.class);
		arp.addMapping("sys_role_menu", "sysRoleMenuId", SysRoleMenu.class);
		arp.addMapping("sys_user", "sysUserId", SysUser.class);
		arp.addMapping("sys_user_role", "sysUserRoleId", SysUserRole.class);
		arp.addMapping("sys_user_session", "sessionId", SysUserSession.class);
		arp.addMapping("templet", "id", Templet.class);
		arp.addMapping("templet_group", "id", TempletGroup.class);
		arp.addMapping("templet_group_i18n", "id", TempletGroupI18n.class);
		arp.addMapping("templet_history", "id", TempletHistory.class);
		arp.addMapping("user", "userId", User.class);
		arp.addMapping("user_bind", "userBindId", UserBind.class);
		arp.addMapping("user_goods", "id", UserGoods.class);
		arp.addMapping("user_preference", "id", UserPreference.class);
		arp.addMapping("user_session", "sessionId", UserSession.class);
		arp.addMapping("user_unregister", "id", UserUnregister.class);
		arp.addMapping("web_help", "helpId", WebHelp.class);
		arp.addMapping("web_help_i18n", "id", WebHelpI18n.class);
	}
}
