package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseUser;
import com.sandu.xinye.common.enums.UserTier;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
public class User extends BaseUser<User> {
	public static final User dao = new User().dao();

	/*
	 * 1-已注册   2-已登录
	 */
	public static final int STATUS_IS_REGISTER = 1;
	public static final int STATUS_IS_LOGINED = 2;

	/*
	 * 0-qq注册登录   1-微信注册登录   2-手机注册登录  3-苹果账号注册 4-一键登录 5-验证码注册登录
	 */
	public static final int REGISTER_TYPE_QQ = 0;
	public static final int REGISTER_TYPE_WX = 1;
	public static final int REGISTER_TYPE_PHONE = 2;
	public static final int REGISTER_TYPE_APPLE_USER = 3;
	public static final int REGISTER_TYPE_ALI_ONES = 4;
	public static final int REGISTER_TYPE_CAPTCHA = 5;

	/**
	 * 获取用户VIP等级枚举
	 */
	public UserTier getUserTierEnum() {
		String tierStr = getUserTier();
		return UserTier.fromString(tierStr);
	}

	/**
	 * 判断是否为VIP用户
	 */
	public boolean isVip() {
		return getUserTierEnum().isVip();
	}

	/**
	 * 获取数据恢复期限描述
	 */
	public String getDataRecoveryPeriodDescription() {
		return getUserTierEnum().getDataRecoveryPeriodDescription();
	}

	/**
	 * 获取数据恢复期限（天数）
	 * @deprecated 使用VipPermissionService获取准确的动态计算天数
	 */
	@Deprecated
	public int getDataRecoveryPeriodDays() {
		return getUserTierEnum().getDataRecoveryPeriodDays();
	}


	/**
	 * 构建VIP信息
	 */
	public java.util.Map<String, Object> buildVipInfo() {
		java.util.Map<String, Object> vipInfo = new java.util.HashMap<>();
		UserTier userTier = getUserTierEnum();

		vipInfo.put("userTier", userTier.name());
		vipInfo.put("userTierDisplay", userTier.getDisplayName());
		vipInfo.put("isVip", userTier.isVip());
		vipInfo.put("dataRecoveryPeriod", userTier.getDataRecoveryPeriodDescription());

		// 如果是免费用户，添加升级提示
		if (!userTier.isVip()) {
			vipInfo.put("upgradeMessage", "升级VIP享受更多高级功能");
		}

		return vipInfo;
	}

	/**
	 * @Title: removeSensitiveInfo
	 * @Description:
	 * @date 2019年3月13日  下午3:14:53
	 * <AUTHOR>
	 */
	public User removeSensitiveInfo() {
		return this.remove("userPass", "salt");
	}
}
