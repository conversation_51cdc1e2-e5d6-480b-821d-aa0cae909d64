package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseFontKindLang<M extends BaseFontKindLang<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setFontKind(java.lang.String fontKind) {
		set("fontKind", fontKind);
		return (M)this;
	}
	
	public java.lang.String getFontKind() {
		return getStr("fontKind");
	}

	public M setFontLang(java.lang.String fontLang) {
		set("fontLang", fontLang);
		return (M)this;
	}
	
	public java.lang.String getFontLang() {
		return getStr("fontLang");
	}

}
