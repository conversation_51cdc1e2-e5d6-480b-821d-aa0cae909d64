package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUser<M extends BaseUser<M>> extends Model<M> implements IBean {

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setUserNickName(java.lang.String userNickName) {
		set("userNickName", userNickName);
		return (M)this;
	}
	
	public java.lang.String getUserNickName() {
		return getStr("userNickName");
	}

	public M setUserPhone(java.lang.String userPhone) {
		set("userPhone", userPhone);
		return (M)this;
	}
	
	public java.lang.String getUserPhone() {
		return getStr("userPhone");
	}

	public M setUserImg(java.lang.String userImg) {
		set("userImg", userImg);
		return (M)this;
	}
	
	public java.lang.String getUserImg() {
		return getStr("userImg");
	}

	public M setUserPass(java.lang.String userPass) {
		set("userPass", userPass);
		return (M)this;
	}
	
	public java.lang.String getUserPass() {
		return getStr("userPass");
	}

	public M setQqId(java.lang.String qqId) {
		set("qqId", qqId);
		return (M)this;
	}
	
	public java.lang.String getQqId() {
		return getStr("qqId");
	}

	public M setWxId(java.lang.String wxId) {
		set("wxId", wxId);
		return (M)this;
	}
	
	public java.lang.String getWxId() {
		return getStr("wxId");
	}

	public M setRegisterType(java.lang.Integer registerType) {
		set("registerType", registerType);
		return (M)this;
	}
	
	public java.lang.Integer getRegisterType() {
		return getInt("registerType");
	}

	public M setSalt(java.lang.String salt) {
		set("salt", salt);
		return (M)this;
	}
	
	public java.lang.String getSalt() {
		return getStr("salt");
	}

	public M setStatus(java.lang.Integer status) {
		set("status", status);
		return (M)this;
	}
	
	public java.lang.Integer getStatus() {
		return getInt("status");
	}

	public M setLastLoginTime(java.util.Date lastLoginTime) {
		set("lastLoginTime", lastLoginTime);
		return (M)this;
	}
	
	public java.util.Date getLastLoginTime() {
		return get("lastLoginTime");
	}

	public M setAppleLoginUserId(java.lang.String appleLoginUserId) {
		set("appleLoginUserId", appleLoginUserId);
		return (M)this;
	}
	
	public java.lang.String getAppleLoginUserId() {
		return getStr("appleLoginUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setOldAppleLoginUserId(java.lang.String oldAppleLoginUserId) {
		set("oldAppleLoginUserId", oldAppleLoginUserId);
		return (M)this;
	}
	
	public java.lang.String getOldAppleLoginUserId() {
		return getStr("oldAppleLoginUserId");
	}

	public M setIdentity(java.lang.String identity) {
		set("identity", identity);
		return (M)this;
	}
	
	public java.lang.String getIdentity() {
		return getStr("identity");
	}

	public M setWxUnionId(java.lang.String wxUnionId) {
		set("wxUnionId", wxUnionId);
		return (M)this;
	}
	
	public java.lang.String getWxUnionId() {
		return getStr("wxUnionId");
	}

	public M setOldQQId(java.lang.String oldQQId) {
		set("oldQQId", oldQQId);
		return (M)this;
	}
	
	public java.lang.String getOldQQId() {
		return getStr("oldQQId");
	}

	public M setQqUnionId(java.lang.String qqUnionId) {
		set("qqUnionId", qqUnionId);
		return (M)this;
	}
	
	public java.lang.String getQqUnionId() {
		return getStr("qqUnionId");
	}

	public M setQqNickName(java.lang.String qqNickName) {
		set("qqNickName", qqNickName);
		return (M)this;
	}

	public java.lang.String getQqNickName() {
		return getStr("qqNickName");
	}

	public M setWxNickName(java.lang.String wxNickName) {
		set("wxNickName", wxNickName);
		return (M)this;
	}

	public java.lang.String getWxNickName() {
		return getStr("wxNickName");
	}

	public M setAppleNickName(java.lang.String appleNickName) {
		set("appleNickName", appleNickName);
		return (M)this;
	}

	public java.lang.String getAppleNickName() {
		return getStr("appleNickName");
	}

	public M setUserTier(java.lang.String userTier) {
		set("userTier", userTier);
		return (M)this;
	}

	public java.lang.String getUserTier() {
		return getStr("userTier");
	}

	public M setPromotionCode(java.lang.String promotionCode) {
		set("promotion_code", promotionCode);
		return (M)this;
	}

	public java.lang.String getPromotionCode() {
		return getStr("promotion_code");
	}

	public M setPromotionBindTime(java.util.Date promotionBindTime) {
		set("promotion_bind_time", promotionBindTime);
		return (M)this;
	}

	public java.util.Date getPromotionBindTime() {
		return get("promotion_bind_time");
	}

}
