package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseSurveyQuestion<M extends BaseSurveyQuestion<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setSurveyId(java.lang.Long surveyId) {
		set("surveyId", surveyId);
		return (M)this;
	}
	
	public java.lang.Long getSurveyId() {
		return getLong("surveyId");
	}

	public M setTitle(java.lang.String title) {
		set("title", title);
		return (M)this;
	}
	
	public java.lang.String getTitle() {
		return getStr("title");
	}

	public M setType(java.lang.Integer type) {
		set("type", type);
		return (M)this;
	}
	
	public java.lang.Integer getType() {
		return getInt("type");
	}

	public M setNote(java.lang.String note) {
		set("note", note);
		return (M)this;
	}
	
	public java.lang.String getNote() {
		return getStr("note");
	}

	public M setHit(java.lang.String hit) {
		set("hit", hit);
		return (M)this;
	}
	
	public java.lang.String getHit() {
		return getStr("hit");
	}

	public M setOptions(java.lang.String options) {
		set("options", options);
		return (M)this;
	}
	
	public java.lang.String getOptions() {
		return getStr("options");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setIsCommon(java.lang.Integer isCommon) {
		set("isCommon", isCommon);
		return (M)this;
	}
	
	public java.lang.Integer getIsCommon() {
		return getInt("isCommon");
	}

}
