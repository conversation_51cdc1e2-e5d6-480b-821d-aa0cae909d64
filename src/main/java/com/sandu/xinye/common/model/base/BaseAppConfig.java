package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseAppConfig<M extends BaseAppConfig<M>> extends Model<M> implements IBean {

	public M setConfigKey(java.lang.String configKey) {
		set("config_key", configKey);
		return (M)this;
	}
	
	public java.lang.String getConfigKey() {
		return getStr("config_key");
	}

	public M setConfigValue(java.lang.String configValue) {
		set("config_value", configValue);
		return (M)this;
	}
	
	public java.lang.String getConfigValue() {
		return getStr("config_value");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
