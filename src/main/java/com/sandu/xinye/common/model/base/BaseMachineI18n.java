package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseMachineI18n<M extends BaseMachineI18n<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setLocale(java.lang.String locale) {
		set("locale", locale);
		return (M)this;
	}
	
	public java.lang.String getLocale() {
		return getStr("locale");
	}

	public M setMachineId(java.lang.Integer machineId) {
		set("machineId", machineId);
		return (M)this;
	}
	
	public java.lang.Integer getMachineId() {
		return getInt("machineId");
	}

	public M setMachineName(java.lang.String machineName) {
		set("machineName", machineName);
		return (M)this;
	}
	
	public java.lang.String getMachineName() {
		return getStr("machineName");
	}

}
