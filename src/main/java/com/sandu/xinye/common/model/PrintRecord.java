package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BasePrintRecord;

/**
 * 打印记录模型
 * 
 * <AUTHOR> by JFinal
 */
@SuppressWarnings("serial")
public class PrintRecord extends BasePrintRecord<PrintRecord> {
	public static final PrintRecord dao = new PrintRecord().dao();
	
	/*
	 * 打印类型常量
	 */
	public static final int PRINT_TYPE_TEMPLATE = 1;  // 模板打印
	public static final int PRINT_TYPE_DOCUMENT = 2;  // 文档打印  
	public static final int PRINT_TYPE_IMAGE = 3;     // 图片打印

	/*
	 * 打印端类型常量
	 */
	public static final int PLATFORM_IOS = 1;         // 手机iOS
	public static final int PLATFORM_ANDROID = 2;     // 手机安卓
	public static final int PLATFORM_WINDOWS = 3;     // 电脑Windows
	public static final int PLATFORM_MAC = 4;         // 电脑Mac

	/*
	 * 打印状态常量
	 */
	public static final int PRINT_STATUS_SUCCESS = 1; // 打印成功
	public static final int PRINT_STATUS_FAILED = 2;  // 打印失败
	
	/**
	 * 获取打印端类型名称
	 * @return 打印端类型名称
	 */
	public String getPrintPlatformName() {
		Integer platform = getPrintPlatform();
		if (platform == null) {
			return "未知";
		}
		
		switch (platform) {
			case PLATFORM_IOS:
				return "手机iOS";
			case PLATFORM_ANDROID:
				return "手机安卓";
			case PLATFORM_WINDOWS:
				return "电脑Windows";
			case PLATFORM_MAC:
				return "电脑Mac";
			default:
				return "未知";
		}
	}
	
	/**
	 * 获取打印状态名称
	 * @return 打印状态名称
	 */
	public String getPrintStatusName() {
		Integer status = getPrintStatus();
		if (status == null) {
			return "未知";
		}
		
		switch (status) {
			case PRINT_STATUS_SUCCESS:
				return "成功";
			case PRINT_STATUS_FAILED:
				return "失败";
			default:
				return "未知";
		}
	}
	
	/**
	 * 获取打印类型名称
	 * @return 打印类型名称
	 */
	public String getPrintTypeName() {
		Integer type = getPrintType();
		if (type == null) {
			return "未知";
		}
		
		switch (type) {
			case PRINT_TYPE_TEMPLATE:
				return "模板打印";
			case PRINT_TYPE_DOCUMENT:
				return "文档打印";
			case PRINT_TYPE_IMAGE:
				return "图片打印";
			default:
				return "未知";
		}
	}
	
	/**
	 * 获取打印尺寸字符串
	 * @return 打印尺寸字符串，格式：宽x高
	 */
	public String getPrintSizeString() {
		Integer width = getPrintWidth();
		Integer height = getPrintHeight();
		
		if (width != null && height != null) {
			return width + "x" + height;
		}
		
		return "";
	}
	
	/**
	 * 判断是否为模板打印记录
	 * @return true-是模板打印记录，false-不是
	 */
	public boolean isTemplatePrint() {
		return Integer.valueOf(PRINT_TYPE_TEMPLATE).equals(getPrintType());
	}

	/**
	 * 判断是否为文档打印记录
	 * @return true-是文档打印记录，false-不是
	 */
	public boolean isDocumentPrint() {
		return Integer.valueOf(PRINT_TYPE_DOCUMENT).equals(getPrintType());
	}

	/**
	 * 判断是否为图片打印记录
	 * @return true-是图片打印记录，false-不是
	 */
	public boolean isImagePrint() {
		return Integer.valueOf(PRINT_TYPE_IMAGE).equals(getPrintType());
	}
	
	/**
	 * 判断是否已删除
	 * @return true-已删除，false-未删除
	 */
	public boolean isDeleted() {
		return getDeleteTime() != null;
	}
}
