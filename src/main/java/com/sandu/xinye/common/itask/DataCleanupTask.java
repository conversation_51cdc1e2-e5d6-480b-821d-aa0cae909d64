package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.cron4j.ITask;
import com.sandu.xinye.common.service.ArchiveBasedDataLifecycleManager;
import com.sandu.xinye.common.service.DataLifecycleManager;

/**
 * 数据清理定时任务
 * 每日执行数据归档和永久删除操作
 *
 * <AUTHOR> Team
 * @since 2024-07-25
 */
public class DataCleanupTask implements ITask {

  private Prop p = PropKit.use("common_config.txt");

  @Override
  public void run() {
    LogKit.info("——————————————————数据清理定时任务开始—————————————————————");

    try {
      // 从配置文件读取数据生命周期参数
      Integer archiveThresholdDays = p.getInt("dataArchiveThresholdDays", 180); // 默认6个月(180天)后归档
      Integer permanentDeleteThresholdDays = p.getInt("dataPermanentDeleteThresholdDays", 365); // 默认1年(365天)后永久删除

      LogKit.info("数据清理配置: 归档阈值=" + archiveThresholdDays + "天, 永久删除阈值=" + permanentDeleteThresholdDays + "天");

      // 创建数据生命周期管理器并执行清理任务
      DataLifecycleManager lifecycleManager = new ArchiveBasedDataLifecycleManager(
          archiveThresholdDays,
          permanentDeleteThresholdDays);

      lifecycleManager.execute();

      LogKit.info("数据清理定时任务执行成功");

    } catch (Exception e) {
      LogKit.error("数据清理定时任务执行失败: " + e.getMessage(), e);
    }

    LogKit.info("——————————————————数据清理定时任务结束—————————————————————");
  }

  @Override
  public void stop() {
    LogKit.info("数据清理定时任务已停止");
  }
}