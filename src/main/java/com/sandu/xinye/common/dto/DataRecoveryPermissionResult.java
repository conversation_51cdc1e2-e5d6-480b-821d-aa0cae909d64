package com.sandu.xinye.common.dto;

import com.sandu.xinye.common.enums.UserTier;
import java.util.Date;

/**
 * 数据恢复权限检查结果
 */
public class DataRecoveryPermissionResult {
    
    private boolean hasPermission;
    private String reason;
    private boolean upgradeRequired;
    private Integer recoveryPeriodDays;
    private UserTier userTier;
    private Date earliestRecoveryDate;
    private String upgradeMessage;
    
    public DataRecoveryPermissionResult() {
    }
    
    public DataRecoveryPermissionResult(boolean hasPermission, String reason) {
        this.hasPermission = hasPermission;
        this.reason = reason;
    }
    
    /**
     * 创建允许访问的结果
     */
    public static DataRecoveryPermissionResult allowed(UserTier userTier, int recoveryPeriodDays, Date earliestRecoveryDate) {
        DataRecoveryPermissionResult result = new DataRecoveryPermissionResult();
        result.hasPermission = true;
        result.userTier = userTier;
        result.recoveryPeriodDays = recoveryPeriodDays;
        result.earliestRecoveryDate = earliestRecoveryDate;
        return result;
    }
    
    /**
     * 创建拒绝访问的结果
     */
    public static DataRecoveryPermissionResult denied(String reason, boolean upgradeRequired, String upgradeMessage) {
        DataRecoveryPermissionResult result = new DataRecoveryPermissionResult();
        result.hasPermission = false;
        result.reason = reason;
        result.upgradeRequired = upgradeRequired;
        result.upgradeMessage = upgradeMessage;
        return result;
    }
    
    /**
     * 创建免费用户的结果
     */
    public static DataRecoveryPermissionResult freeUser(Date earliestRecoveryDate, int actualDays) {
        DataRecoveryPermissionResult result = new DataRecoveryPermissionResult();
        result.hasPermission = true;
        result.userTier = UserTier.FREE;
        result.recoveryPeriodDays = actualDays;
        result.earliestRecoveryDate = earliestRecoveryDate;
        result.upgradeMessage = "升级VIP可恢复6个月内的数据";
        return result;
    }

    /**
     * 创建VIP用户的结果
     */
    public static DataRecoveryPermissionResult vipUser(UserTier userTier, Date earliestRecoveryDate, int actualDays) {
        DataRecoveryPermissionResult result = new DataRecoveryPermissionResult();
        result.hasPermission = true;
        result.userTier = userTier;
        result.recoveryPeriodDays = actualDays;
        result.earliestRecoveryDate = earliestRecoveryDate;
        return result;
    }

    /**
     * 创建免费用户的结果（兼容旧方法）
     */
    public static DataRecoveryPermissionResult freeUser(Date earliestRecoveryDate) {
        return freeUser(earliestRecoveryDate, 7);
    }

    /**
     * 创建VIP用户的结果（兼容旧方法）
     */
    public static DataRecoveryPermissionResult vipUser(UserTier userTier, Date earliestRecoveryDate) {
        // 计算实际天数
        Date now = new Date();
        long diffInMillis = now.getTime() - earliestRecoveryDate.getTime();
        int actualDays = (int) (diffInMillis / (24 * 60 * 60 * 1000));
        return vipUser(userTier, earliestRecoveryDate, actualDays);
    }
    
    // Getters and Setters
    public boolean isHasPermission() {
        return hasPermission;
    }
    
    public void setHasPermission(boolean hasPermission) {
        this.hasPermission = hasPermission;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public boolean isUpgradeRequired() {
        return upgradeRequired;
    }
    
    public void setUpgradeRequired(boolean upgradeRequired) {
        this.upgradeRequired = upgradeRequired;
    }
    
    public Integer getRecoveryPeriodDays() {
        return recoveryPeriodDays;
    }
    
    public void setRecoveryPeriodDays(Integer recoveryPeriodDays) {
        this.recoveryPeriodDays = recoveryPeriodDays;
    }
    
    public UserTier getUserTier() {
        return userTier;
    }
    
    public void setUserTier(UserTier userTier) {
        this.userTier = userTier;
    }
    
    public Date getEarliestRecoveryDate() {
        return earliestRecoveryDate;
    }
    
    public void setEarliestRecoveryDate(Date earliestRecoveryDate) {
        this.earliestRecoveryDate = earliestRecoveryDate;
    }
    
    public String getUpgradeMessage() {
        return upgradeMessage;
    }
    
    public void setUpgradeMessage(String upgradeMessage) {
        this.upgradeMessage = upgradeMessage;
    }
}
