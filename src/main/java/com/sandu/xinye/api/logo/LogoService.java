package com.sandu.xinye.api.logo;

import java.util.List;

import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Logo;
import com.sandu.xinye.common.model.LogoChildKind;
import com.sandu.xinye.common.model.LogoKind;

public class LogoService {

    public static final LogoService me = new LogoService();

    /*
     * 语言类型
     */
    private static final int LANGUAGE_SYSTEM = 0;
    private static final int LANGUAGE_CHINESE = 1;
    private static final int LANGUAGE_ENGLISH = 2;
    private static final int LANGUAGE_TRADITION = 3;
    private static final int LANGUAGE_KOREAN = 4;

    public RetKit getLogoList(Integer language) {
        if (language != LANGUAGE_CHINESE && language != LANGUAGE_ENGLISH && language != LANGUAGE_TRADITION && language != LANGUAGE_KOREAN) {
            return RetKit.fail("语言类型传入有误！");
        }
        List<Logo> list = Logo.dao.find("SELECT g.logoId, g.logoImg FROM logo g inner join logo_kind t on t.logoKindId = g.logoKindId " +
                " where g.version = 1 order by t.createTime asc");
        for (Logo logo : list) {
            LogoKind kind = LogoKind.dao.findById(logo.getLogoKindId());
            LogoChildKind childKind = LogoChildKind.dao.findById(logo.getLogoChildKindId());
            String groupName = "";
            if (language == LANGUAGE_TRADITION) {
                groupName = kind.getTraditionalName() + "/" + childKind.getTraditionalName();
            } else if (language == LANGUAGE_ENGLISH) {
                groupName = kind.getEnglishName() + "/" + childKind.getEnglishName();
            } else if (language == LANGUAGE_KOREAN) {
                groupName = kind.getKoreanName() + "/" + childKind.getKoreanName();
            } else {
                groupName = kind.getLogoKindName() + "/" + childKind.getLogoChildKindName();
            }
            logo.put("groupName", groupName);
        }
        return RetKit.ok("items", list);
    }

}
