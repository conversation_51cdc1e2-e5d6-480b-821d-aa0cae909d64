package com.sandu.xinye.api.banner;

import java.util.List;

import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Banner;

public class BannerApiService {

	public static final BannerApiService me = new BannerApiService();

	public RetKit list() {
		List<Banner> list = Banner.dao.find("select * from banner order by id desc");
		for(Banner banner:list){
			String cover = banner.getCover();
			if(cover.startsWith("http")){
				cover = cover.replace(Constant.REAL_UPLOAD_IP, "/upload");
				banner.setCover(cover);
			}
		}
		return RetKit.ok("list", list);
	}

}
