package com.sandu.xinye.api.v2.app_version;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.GlobalInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.xiaoleilu.hutool.util.ObjectUtil;
import com.xiaoleilu.hutool.util.StrUtil;

public class AppVersionController extends AppController {

    @Clear
    public void getNewestVersion() {
        Integer version = getParaToInt("version");
        if (ObjectUtil.isNotNull(version)) {
            RetKit ret = AppVersionService.me.getNewestVersionTwo();
            renderJson(ret);
        } else {
            RetKit ret = AppVersionService.me.getNewestVersion();
            renderJson(ret);
        }
    }

    @Clear
    @Before(GlobalInterceptor.class)
    public void downloadApk() {
        String url = AppVersionService.me.downloadApkUrl();
        renderJson(RetKit.ok("url", url));
    }

}
