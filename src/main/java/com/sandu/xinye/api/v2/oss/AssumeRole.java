package com.sandu.xinye.api.v2.oss;

/**
 * STS 凭证
 *
 * <AUTHOR>
 * @date 2024/1/2
 */
public class AssumeRole {
    private String StatusCode;
    private String ErrorCode;
    private String ErrorMessage;

    private String AccessKeyId;
    private String AccessKeySecret;
    private String SecurityToken;
    private String Expiration;

    public void setStatusCode(java.lang.String StatusCode) {
        this.StatusCode = StatusCode;
    }
    public java.lang.String getStatusCode() {
        return this.StatusCode;
    }

    public void setErrorCode(java.lang.String ErrorCode) {
        this.ErrorCode = ErrorCode;
    }
    public java.lang.String getErrorCode() {
        return this.ErrorCode;
    }

    public void setErrorMessage(java.lang.String ErrorMessage) {
        this.ErrorMessage = ErrorMessage;
    }
    public java.lang.String getErrorMessage() {
        return this.ErrorMessage;
    }

    public void setAccessKeyId(java.lang.String AccessKeyId) {
        this.AccessKeyId = AccessKeyId;
    }
    public java.lang.String getAccessKeyId() {
        return this.AccessKeyId;
    }

    public void setAccessKeySecret(java.lang.String AccessKeySecret) {
        this.AccessKeySecret = AccessKeySecret;
    }
    public java.lang.String getAccessKeySecret() {
        return this.AccessKeySecret;
    }

    public void setSecurityToken(java.lang.String SecurityToken) {
        this.SecurityToken = SecurityToken;
    }
    public java.lang.String getSecurityToken() {
        return this.SecurityToken;
    }

    public void setExpiration(java.lang.String Expiration) {
        this.Expiration = Expiration;
    }
    public java.lang.String getExpiration() {
        return this.Expiration;
    }
}
