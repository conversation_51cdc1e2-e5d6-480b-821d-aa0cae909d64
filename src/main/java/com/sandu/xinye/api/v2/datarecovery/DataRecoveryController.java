package com.sandu.xinye.api.v2.datarecovery;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

/**
 * 数据恢复API控制器
 * 提供已删除数据的查询和恢复功能
 * 
 * <AUTHOR> Team
 * @since 2024-07-21
 */
public class DataRecoveryController extends AppController {

    /**
     * 获取用户的已删除数据列表（分页）
     * 
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param pageNumber 页码，默认1
     * @param pageSize 每页大小，默认10
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     */
    public void getDeletedDataList() {
        LogKit.info("获取已删除数据列表接口开始--------------------");
        
        String dataType = getPara("dataType");
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        
        User user = getUser();
        RetKit ret = DataRecoveryService.me.getDeletedDataList(
            user.getUserId(), dataType, pageNumber, pageSize, startDate, endDate
        );
        renderJson(ret);
        
        LogKit.info("获取已删除数据列表接口结束--------------------");
    }

    /**
     * 获取已删除数据的详细信息
     * 
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param dataId 数据ID
     */
    public void getDeletedDataDetail() {
        LogKit.info("获取已删除数据详情接口开始--------------------");
        
        String dataType = getPara("dataType");
        String dataId = getPara("dataId");
        
        User user = getUser();
        RetKit ret = DataRecoveryService.me.getDeletedDataDetail(
            user.getUserId(), dataType, dataId
        );
        renderJson(ret);
        
        LogKit.info("获取已删除数据详情接口结束--------------------");
    }

    /**
     * 恢复单个已删除的数据
     *
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param dataId 数据ID
     */
    @Before({PostOnlyInterceptor.class})
    @OperationLog(modelName = "data_recovery")
    public void recoverSingleData() {
        LogKit.info("恢复单个数据接口开始--------------------");

        Record bodyPara = getArgsRecord();
        String dataType = bodyPara.getStr("dataType");
        String dataId = bodyPara.getStr("dataId");

        User user = getUser();
        RetKit ret = DataRecoveryService.me.recoverSingleData(
            user.getUserId(), dataType, dataId
        );
        renderJson(ret);

        LogKit.info("恢复单个数据接口结束--------------------");
    }

    /**
     * 批量恢复已删除的数据
     *
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param dataIds 数据ID列表，逗号分隔
     */
    @Before({PostOnlyInterceptor.class})
    @OperationLog(modelName = "data_recovery")
    public void recoverBatchData() {
        LogKit.info("批量恢复数据接口开始--------------------");

        Record bodyPara = getArgsRecord();
        String dataType = bodyPara.getStr("dataType");
        String dataIds = bodyPara.getStr("dataIds");

        User user = getUser();
        RetKit ret = DataRecoveryService.me.recoverBatchData(
            user.getUserId(), dataType, dataIds
        );
        renderJson(ret);

        LogKit.info("批量恢复数据接口结束--------------------");
    }

    /**
     * 按时间范围批量恢复数据
     *
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    @Before({PostOnlyInterceptor.class})
    @OperationLog(modelName = "data_recovery")
    public void recoverDataByDateRange() {
        LogKit.info("按时间范围恢复数据接口开始--------------------");

        Record bodyPara = getArgsRecord();
        String dataType = bodyPara.getStr("dataType");
        String startDate = bodyPara.getStr("startDate");
        String endDate = bodyPara.getStr("endDate");

        User user = getUser();
        RetKit ret = DataRecoveryService.me.recoverDataByDateRange(
            user.getUserId(), dataType, startDate, endDate
        );
        renderJson(ret);

        LogKit.info("按时间范围恢复数据接口结束--------------------");
    }

    /**
     * 永久删除已删除的数据
     * 
     * @param dataType 数据类型：template, template_group, cloudfile
     * @param dataId 数据ID
     */
    @Before({PostOnlyInterceptor.class})
    @OperationLog(modelName = "data_recovery")
    public void permanentDeleteData() {
        LogKit.info("永久删除数据接口开始--------------------");

        Record bodyPara = getArgsRecord();
        String dataType = bodyPara.getStr("dataType");
        String dataId = bodyPara.getStr("dataId");
        
        User user = getUser();
        RetKit ret = DataRecoveryService.me.permanentDeleteData(
            user.getUserId(), dataType, dataId
        );
        renderJson(ret);
        
        LogKit.info("永久删除数据接口结束--------------------");
    }

    /**
     * 获取用户数据恢复权限信息
     */
    public void getRecoveryPermission() {
        LogKit.info("获取数据恢复权限接口开始--------------------");

        User user = getUser();
        RetKit ret = DataRecoveryService.me.getRecoveryPermission(user.getUserId());
        renderJson(ret);

        LogKit.info("获取数据恢复权限接口结束--------------------");
    }

    /**
     * 获取数据恢复统计信息
     * 包括各类型已删除数据的数量统计
     */
    public void getRecoveryStatistics() {
        LogKit.info("获取数据恢复统计接口开始--------------------");

        User user = getUser();
        RetKit ret = DataRecoveryService.me.getRecoveryStatistics(user.getUserId());
        renderJson(ret);

        LogKit.info("获取数据恢复统计接口结束--------------------");
    }

    /**
     * 获取数据恢复操作历史记录
     * 
     * @param pageNumber 页码，默认1
     * @param pageSize 每页大小，默认10
     */
    public void getRecoveryHistory() {
        LogKit.info("获取数据恢复历史接口开始--------------------");
        
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        
        User user = getUser();
        RetKit ret = DataRecoveryService.me.getRecoveryHistory(
            user.getUserId(), pageNumber, pageSize
        );
        renderJson(ret);
        
        LogKit.info("获取数据恢复历史接口结束--------------------");
    }
}
