package com.sandu.xinye.api.v2.datarecovery;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.dto.DataRecoveryPermissionResult;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Cloudfile;
import com.sandu.xinye.common.model.DataRecoveryLogs;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import com.sandu.xinye.common.service.VipPermissionService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据恢复服务类
 * 提供已删除数据的查询、恢复、永久删除等功能
 * 
 * <AUTHOR> Team
 * @since 2024-07-21
 */
public class DataRecoveryService {

    public static final DataRecoveryService me = new DataRecoveryService();

    // 支持的数据类型常量
    public static final String DATA_TYPE_TEMPLATE = "template";
    public static final String DATA_TYPE_TEMPLATE_GROUP = "template_group";
    public static final String DATA_TYPE_CLOUDFILE = "cloudfile";

    // 恢复类型常量
    public static final String RECOVERY_TYPE_SINGLE = "single";
    public static final String RECOVERY_TYPE_BATCH = "batch";
    public static final String RECOVERY_TYPE_BATCH_BY_DATE = "batch_by_date";

    /**
     * 获取用户的已删除数据列表（分页）
     */
    public RetKit getDeletedDataList(Integer userId, String dataType, int pageNumber, int pageSize,
            String startDate, String endDate) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        if (StrKit.isBlank(dataType)) {
            return RetKit.fail("数据类型不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            // 检查用户数据恢复权限
            DataRecoveryPermissionResult permissionResult = VipPermissionService.me.checkDataRecoveryPermission(userId);
            if (!permissionResult.isHasPermission()) {
                return RetKit.fail(permissionResult.getReason());
            }

            String tableName = getTableName(dataType);
            String primaryKey = getPrimaryKey(dataType);

            StringBuilder selectSql = new StringBuilder();
            StringBuilder fromSql = new StringBuilder();

            // 构建 SELECT 部分
            selectSql.append("SELECT ").append(primaryKey).append(" as id, ");

            // 根据数据类型选择不同的字段
            if (DATA_TYPE_TEMPLATE.equals(dataType)) {
                selectSql.append("name, cover, width, height, createTime, deleteTime, type");
                fromSql.append("FROM templet");
            } else if (DATA_TYPE_TEMPLATE_GROUP.equals(dataType)) {
                selectSql.append("name, createTime, deleteTime, type");
                fromSql.append("FROM templet_group");
            } else if (DATA_TYPE_CLOUDFILE.equals(dataType)) {
                selectSql.append("name, url, type, createTime, deleteTime");
                fromSql.append("FROM cloudfile");
            }

            // 构建 WHERE 部分
            fromSql.append(" WHERE userId = ? AND deleteTime IS NOT NULL");

            // 添加VIP权限的时间限制
            fromSql.append(" AND deleteTime >= ?");

            List<Object> params = new ArrayList<>();
            params.add(userId);
            params.add(permissionResult.getEarliestRecoveryDate());

            // 添加时间范围过滤
            if (StrKit.notBlank(startDate)) {
                fromSql.append(" AND deleteTime >= ?");
                params.add(startDate + " 00:00:00");
            }
            if (StrKit.notBlank(endDate)) {
                fromSql.append(" AND deleteTime <= ?");
                params.add(endDate + " 23:59:59");
            }

            fromSql.append(" ORDER BY deleteTime DESC");

            Page<Record> page = Db.paginate(pageNumber, pageSize,
                    selectSql.toString(),
                    fromSql.toString(),
                    params.toArray());

            // 输出params
            LogKit.info("params: " + params);

            // 构建返回结果
            return RetKit.ok("page", page);

        } catch (Exception e) {
            LogKit.error("获取已删除数据列表异常: " + e.getMessage(), e);
            return RetKit.fail("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取已删除数据的详细信息
     */
    public RetKit getDeletedDataDetail(Integer userId, String dataType, String dataId) {
        if (userId == null || StrKit.isBlank(dataType) || StrKit.isBlank(dataId)) {
            return RetKit.fail("参数不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            Record data = null;
            Date deleteTime = null;

            if (DATA_TYPE_TEMPLATE.equals(dataType)) {
                Templet template = Templet.dao.findById(dataId);
                if (template == null || !template.getUserId().equals(userId) || template.get("deleteTime") == null) {
                    return RetKit.fail("数据不存在或无权限访问");
                }
                deleteTime = template.get("deleteTime");

                // 检查是否在可查看时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, deleteTime)) {
                    return RetKit.fail("数据超出可查看时间范围，请升级VIP获得更长查看期限");
                }

                data = template.toRecord();
            } else if (DATA_TYPE_TEMPLATE_GROUP.equals(dataType)) {
                TempletGroup group = TempletGroup.dao.findById(dataId);
                if (group == null || !group.getUserId().equals(userId) || group.get("deleteTime") == null) {
                    return RetKit.fail("数据不存在或无权限访问");
                }
                deleteTime = group.get("deleteTime");

                // 检查是否在可查看时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, deleteTime)) {
                    return RetKit.fail("数据超出可查看时间范围，请升级VIP获得更长查看期限");
                }

                data = group.toRecord();
            } else if (DATA_TYPE_CLOUDFILE.equals(dataType)) {
                Cloudfile file = Cloudfile.dao.findById(dataId);
                if (file == null || !file.getUserId().equals(userId) || file.get("deleteTime") == null) {
                    return RetKit.fail("数据不存在或无权限访问");
                }
                deleteTime = file.get("deleteTime");

                // 检查是否在可查看时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, deleteTime)) {
                    return RetKit.fail("数据超出可查看时间范围，请升级VIP获得更长查看期限");
                }

                data = file.toRecord();
            }

            return RetKit.ok("data", data);

        } catch (Exception e) {
            LogKit.error("获取已删除数据详情异常: " + e.getMessage(), e);
            return RetKit.fail("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 恢复单个已删除的数据
     */
    public RetKit recoverSingleData(Integer userId, String dataType, String dataId) {
        if (userId == null || StrKit.isBlank(dataType) || StrKit.isBlank(dataId)) {
            return RetKit.fail("参数不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            boolean success = false;
            String targetName = "";
            Date originalDeleteTime = null;

            if (DATA_TYPE_TEMPLATE.equals(dataType)) {
                Templet template = Templet.dao.findById(dataId);
                if (template == null || !template.getUserId().equals(userId) || template.get("deleteTime") == null) {
                    return RetKit.fail("模板不存在或无权限访问");
                }
                originalDeleteTime = template.get("deleteTime");

                // 检查是否在可恢复时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可恢复时间范围，请升级VIP获得更长恢复期限");
                }

                // 检查模板分组状态，如果分组已删除则调整为未分组
                RetKit groupAdjustResult = checkAndAdjustTemplateGroup(template, userId);
                if (!groupAdjustResult.success()) {
                    return groupAdjustResult;
                }

                targetName = template.getName();
                template.set("deleteTime", null);
                success = template.update();

                // 如果分组被调整了，更新返回消息
                String adjustmentMsg = groupAdjustResult.getStr("adjustmentMessage");
                if (StrKit.notBlank(adjustmentMsg)) {
                    targetName = template.getName() + "（" + adjustmentMsg + "）";
                }
            } else if (DATA_TYPE_TEMPLATE_GROUP.equals(dataType)) {
                TempletGroup group = TempletGroup.dao.findById(dataId);
                if (group == null || !group.getUserId().equals(userId) || group.get("deleteTime") == null) {
                    return RetKit.fail("分组不存在或无权限访问");
                }
                originalDeleteTime = group.get("deleteTime");

                // 检查是否在可恢复时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可恢复时间范围，请升级VIP获得更长恢复期限");
                }

                targetName = group.getName();
                group.set("deleteTime", null);
                success = group.update();
            } else if (DATA_TYPE_CLOUDFILE.equals(dataType)) {
                Cloudfile file = Cloudfile.dao.findById(dataId);
                if (file == null || !file.getUserId().equals(userId) || file.get("deleteTime") == null) {
                    return RetKit.fail("文件不存在或无权限访问");
                }
                originalDeleteTime = file.get("deleteTime");

                // 检查是否在可恢复时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可恢复时间范围，请升级VIP获得更长恢复期限");
                }

                targetName = file.getName();
                file.set("deleteTime", null);
                success = file.update();
            }

            if (success) {
                // 记录恢复日志
                logRecoveryOperation(userId, dataType, Integer.valueOf(dataId), targetName,
                        RECOVERY_TYPE_SINGLE, "success", null, originalDeleteTime);

                LogKit.info("数据恢复成功: dataType=" + dataType + ", dataId=" + dataId + ", userId=" + userId);
                return RetKit.ok("数据恢复成功");
            } else {
                return RetKit.fail("恢复失败");
            }

        } catch (Exception e) {
            LogKit.error("恢复数据异常: " + e.getMessage(), e);

            // 记录失败日志
            try {
                logRecoveryOperation(userId, dataType, Integer.valueOf(dataId), "",
                        RECOVERY_TYPE_SINGLE, "failed", e.getMessage(), null);
            } catch (Exception logEx) {
                LogKit.error("记录恢复日志失败: " + logEx.getMessage(), logEx);
            }

            return RetKit.fail("恢复失败: " + e.getMessage());
        }
    }

    /**
     * 批量恢复已删除的数据
     */
    public RetKit recoverBatchData(Integer userId, String dataType, String dataIds) {
        if (userId == null || StrKit.isBlank(dataType) || StrKit.isBlank(dataIds)) {
            return RetKit.fail("参数不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            String[] idArray = dataIds.split(",");
            int successCount = 0;
            int failCount = 0;
            List<String> failedIds = new ArrayList<>();

            for (String id : idArray) {
                if (StrKit.isBlank(id.trim())) {
                    continue;
                }

                RetKit result = recoverSingleData(userId, dataType, id.trim());
                if (result.success()) {
                    successCount++;
                } else {
                    failCount++;
                    failedIds.add(id.trim());
                    LogKit.warn("批量恢复失败: dataId=" + id + ", error=" + result.getMsg());
                }
            }

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("successCount", successCount);
            resultData.put("failCount", failCount);
            resultData.put("failedIds", failedIds);

            if (failCount == 0) {
                return RetKit.ok("批量恢复成功").set("data", resultData);
            } else if (successCount == 0) {
                return RetKit.fail("批量恢复失败").set("data", resultData);
            } else {
                return RetKit.ok("部分恢复成功").set("data", resultData);
            }

        } catch (Exception e) {
            LogKit.error("批量恢复数据异常: " + e.getMessage(), e);
            return RetKit.fail("批量恢复失败: " + e.getMessage());
        }
    }

    /**
     * 记录恢复操作日志
     */
    private void logRecoveryOperation(Integer userId, String dataType, Integer targetId, String targetName,
            String recoveryType, String status, String errorMessage, Date originalDeleteTime) {
        try {
            DataRecoveryLogs log = new DataRecoveryLogs();
            log.setUserId(userId);
            log.setDataType(dataType);
            log.setTargetId(targetId);
            log.setTargetName(targetName);
            log.setRecoveryType(recoveryType);
            log.setRecoveryStatus(status);
            log.setErrorMessage(errorMessage);
            log.setRecoveryTime(new Date());
            log.setOriginalDeleteTime(originalDeleteTime);
            log.save();
        } catch (Exception e) {
            LogKit.error("记录恢复日志异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证数据类型是否有效
     */
    private boolean isValidDataType(String dataType) {
        return DATA_TYPE_TEMPLATE.equals(dataType) ||
                DATA_TYPE_TEMPLATE_GROUP.equals(dataType) ||
                DATA_TYPE_CLOUDFILE.equals(dataType);
    }

    /**
     * 根据数据类型获取表名
     */
    private String getTableName(String dataType) {
        switch (dataType) {
            case DATA_TYPE_TEMPLATE:
                return "templet";
            case DATA_TYPE_TEMPLATE_GROUP:
                return "templet_group";
            case DATA_TYPE_CLOUDFILE:
                return "cloudfile";
            default:
                return null;
        }
    }

    /**
     * 根据数据类型获取主键字段名
     */
    private String getPrimaryKey(String dataType) {
        switch (dataType) {
            case DATA_TYPE_TEMPLATE:
            case DATA_TYPE_TEMPLATE_GROUP:
            case DATA_TYPE_CLOUDFILE:
                return "id";
            default:
                return "id";
        }
    }

    /**
     * 按时间范围批量恢复数据
     */
    public RetKit recoverDataByDateRange(Integer userId, String dataType, String startDate, String endDate) {
        if (userId == null || StrKit.isBlank(dataType) || StrKit.isBlank(startDate) || StrKit.isBlank(endDate)) {
            return RetKit.fail("参数不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            // 检查用户数据恢复权限
            DataRecoveryPermissionResult permissionResult = VipPermissionService.me.checkDataRecoveryPermission(userId);
            if (!permissionResult.isHasPermission()) {
                return RetKit.fail(permissionResult.getReason());
            }

            String tableName = getTableName(dataType);
            String primaryKey = getPrimaryKey(dataType);

            // 查询指定时间范围内的已删除数据，并添加VIP权限的时间限制
            String sql = "SELECT " + primaryKey + " FROM " + tableName +
                    " WHERE userId = ? AND deleteTime IS NOT NULL " +
                    " AND deleteTime >= ? AND deleteTime <= ? AND deleteTime >= ?";

            List<Record> records = Db.find(sql, userId, startDate + " 00:00:00", endDate + " 23:59:59",
                    permissionResult.getEarliestRecoveryDate());

            if (records.isEmpty()) {
                return RetKit.fail("指定时间范围内没有已删除的数据");
            }

            // 构建ID列表
            List<String> idList = new ArrayList<>();
            for (Record record : records) {
                idList.add(record.get(primaryKey).toString());
            }

            String dataIds = String.join(",", idList);

            // 调用批量恢复方法
            RetKit result = recoverBatchData(userId, dataType, dataIds);

            // 记录按时间范围恢复的日志
            if (result.success()) {
                logRecoveryOperation(userId, dataType, 0, "时间范围: " + startDate + " 至 " + endDate,
                        RECOVERY_TYPE_BATCH_BY_DATE, "success", null, null);
            }

            return result;

        } catch (Exception e) {
            LogKit.error("按时间范围恢复数据异常: " + e.getMessage(), e);
            return RetKit.fail("恢复失败: " + e.getMessage());
        }
    }

    /**
     * 永久删除已删除的数据
     */
    public RetKit permanentDeleteData(Integer userId, String dataType, String dataId) {
        if (userId == null || StrKit.isBlank(dataType) || StrKit.isBlank(dataId)) {
            return RetKit.fail("参数不能为空");
        }

        if (!isValidDataType(dataType)) {
            return RetKit.fail("不支持的数据类型");
        }

        try {
            boolean success = false;
            Date originalDeleteTime = null;

            if (DATA_TYPE_TEMPLATE.equals(dataType)) {
                Templet template = Templet.dao.findById(dataId);
                if (template == null || !template.getUserId().equals(userId) || template.get("deleteTime") == null) {
                    return RetKit.fail("模板不存在或无权限访问");
                }
                originalDeleteTime = template.get("deleteTime");

                // 检查是否在可操作时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可操作时间范围，请升级VIP获得更长操作期限");
                }

                success = template.delete();
            } else if (DATA_TYPE_TEMPLATE_GROUP.equals(dataType)) {
                TempletGroup group = TempletGroup.dao.findById(dataId);
                if (group == null || !group.getUserId().equals(userId) || group.get("deleteTime") == null) {
                    return RetKit.fail("分组不存在或无权限访问");
                }
                originalDeleteTime = group.get("deleteTime");

                // 检查是否在可操作时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可操作时间范围，请升级VIP获得更长操作期限");
                }

                success = group.delete();
            } else if (DATA_TYPE_CLOUDFILE.equals(dataType)) {
                Cloudfile file = Cloudfile.dao.findById(dataId);
                if (file == null || !file.getUserId().equals(userId) || file.get("deleteTime") == null) {
                    return RetKit.fail("文件不存在或无权限访问");
                }
                originalDeleteTime = file.get("deleteTime");

                // 检查是否在可操作时间范围内
                if (!VipPermissionService.me.canRecoverDataFromDate(userId, originalDeleteTime)) {
                    return RetKit.fail("数据超出可操作时间范围，请升级VIP获得更长操作期限");
                }

                success = file.delete();
            }

            if (success) {
                LogKit.info("数据永久删除成功: dataType=" + dataType + ", dataId=" + dataId + ", userId=" + userId);
                return RetKit.ok("数据永久删除成功");
            } else {
                return RetKit.fail("永久删除失败");
            }

        } catch (Exception e) {
            LogKit.error("永久删除数据异常: " + e.getMessage(), e);
            return RetKit.fail("永久删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据恢复统计信息
     * 包含待恢复数据、已恢复数据和综合统计信息
     */
    public RetKit getRecoveryStatistics(Integer userId) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        try {
            // 检查用户数据恢复权限
            DataRecoveryPermissionResult permissionResult = VipPermissionService.me.checkDataRecoveryPermission(userId);
            if (!permissionResult.isHasPermission()) {
                return RetKit.fail(permissionResult.getReason());
            }

            Map<String, Object> statistics = new HashMap<>();

            // ==================== 待恢复数据统计 ====================
            Map<String, Object> pendingStats = new HashMap<>();

            // 统计各类型已删除数据数量（只统计在权限范围内的数据）
            Long deletedTemplateCount = Db.queryLong(
                    "SELECT COUNT(*) FROM templet WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= ?",
                    userId, permissionResult.getEarliestRecoveryDate());
            Long deletedGroupCount = Db.queryLong(
                    "SELECT COUNT(*) FROM templet_group WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= ?",
                    userId, permissionResult.getEarliestRecoveryDate());
            Long deletedFileCount = Db.queryLong(
                    "SELECT COUNT(*) FROM cloudfile WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= ?",
                    userId, permissionResult.getEarliestRecoveryDate());

            pendingStats.put("deletedTemplateCount", deletedTemplateCount);
            pendingStats.put("deletedGroupCount", deletedGroupCount);
            pendingStats.put("deletedFileCount", deletedFileCount);
            pendingStats.put("totalDeletedCount", deletedTemplateCount + deletedGroupCount + deletedFileCount);

            // 统计最近7天的删除数据
            Long recentDeletedCount = Db.queryLong(
                    "SELECT COUNT(*) FROM (" +
                            "  SELECT deleteTime FROM templet WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
                            +
                            "  UNION ALL" +
                            "  SELECT deleteTime FROM templet_group WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
                            +
                            "  UNION ALL" +
                            "  SELECT deleteTime FROM cloudfile WHERE userId = ? AND deleteTime IS NOT NULL AND deleteTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
                            +
                            ") AS recent_deleted",
                    userId, userId, userId);

            pendingStats.put("recentDeletedCount", recentDeletedCount);

            // ==================== 已恢复数据统计 ====================
            Map<String, Object> recoveredStats = new HashMap<>();

            // 统计历史恢复数据总量
            Long totalRecoveredCount = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ? AND recoveryStatus = 'success'", userId);

            // 按类型统计已恢复数据
            Long recoveredTemplateCount = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ? AND dataType = 'template' AND recoveryStatus = 'success'",
                    userId);
            Long recoveredGroupCount = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ? AND dataType = 'template_group' AND recoveryStatus = 'success'",
                    userId);
            Long recoveredFileCount = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ? AND dataType = 'cloudfile' AND recoveryStatus = 'success'",
                    userId);

            // 统计最近7天恢复的数据
            Long recentRecoveredCount = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ? AND recoveryStatus = 'success' AND recoveryTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
                    userId);

            recoveredStats.put("totalRecoveredCount", totalRecoveredCount);
            recoveredStats.put("recoveredTemplateCount", recoveredTemplateCount);
            recoveredStats.put("recoveredGroupCount", recoveredGroupCount);
            recoveredStats.put("recoveredFileCount", recoveredFileCount);
            recoveredStats.put("recentRecoveredCount", recentRecoveredCount);

            // ==================== 综合统计信息 ====================
            Map<String, Object> summaryStats = new HashMap<>();

            // 计算总处理数据量
            Long totalDataProcessed = totalRecoveredCount
                    + (deletedTemplateCount + deletedGroupCount + deletedFileCount);

            // 计算恢复成功率
            Long totalRecoveryAttempts = Db.queryLong(
                    "SELECT COUNT(*) FROM data_recovery_logs WHERE userId = ?", userId);
            String recoverySuccessRate = "0%";
            if (totalRecoveryAttempts > 0) {
                double successRate = (totalRecoveredCount.doubleValue() / totalRecoveryAttempts.doubleValue()) * 100;
                recoverySuccessRate = String.format("%.1f%%", successRate);
            }

            // 获取最后一次恢复时间
            String lastRecoveryTime = Db.queryStr(
                    "SELECT DATE_FORMAT(recoveryTime, '%Y-%m-%d %H:%i:%s') FROM data_recovery_logs WHERE userId = ? AND recoveryStatus = 'success' ORDER BY recoveryTime DESC LIMIT 1",
                    userId);

            summaryStats.put("totalDataProcessed", totalDataProcessed);
            summaryStats.put("recoverySuccessRate", recoverySuccessRate);
            summaryStats.put("totalRecoveryAttempts", totalRecoveryAttempts);
            summaryStats.put("lastRecoveryTime", lastRecoveryTime);

            // ==================== 组装返回结果 ====================
            statistics.put("pending", pendingStats);
            statistics.put("recovered", recoveredStats);
            statistics.put("summary", summaryStats);
            statistics.put("permission", buildPermissionInfo(permissionResult));

            return RetKit.ok("statistics", statistics);

        } catch (Exception e) {
            LogKit.error("获取恢复统计信息异常: " + e.getMessage(), e);
            return RetKit.fail("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据恢复操作历史记录
     */
    public RetKit getRecoveryHistory(Integer userId, int pageNumber, int pageSize) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        try {
            Page<DataRecoveryLogs> page = DataRecoveryLogs.dao.paginate(
                    pageNumber, pageSize,
                    "SELECT *",
                    "FROM data_recovery_logs WHERE userId = ? ORDER BY recoveryTime DESC",
                    userId);

            return RetKit.ok("page", page);

        } catch (Exception e) {
            LogKit.error("获取恢复历史记录异常: " + e.getMessage(), e);
            return RetKit.fail("获取历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户数据恢复权限信息
     */
    public RetKit getRecoveryPermission(Integer userId) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        try {
            DataRecoveryPermissionResult permissionResult = VipPermissionService.me.checkDataRecoveryPermission(userId);
            return RetKit.ok("permission", buildPermissionInfo(permissionResult));

        } catch (Exception e) {
            LogKit.error("获取数据恢复权限信息异常: " + e.getMessage(), e);
            return RetKit.fail("获取权限信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查模板分组状态并调整 groupId（如果分组已删除则设为未分组）
     */
    private RetKit checkAndAdjustTemplateGroup(Templet template, Integer userId) {
        try {
            Integer groupId = template.getGroupId();

            // 如果没有分组或分组ID为-1（表示无分组），直接返回成功
            if (groupId == null || groupId == -1) {
                return RetKit.ok();
            }

            // 查询分组信息
            TempletGroup group = TempletGroup.dao.findById(groupId);

            // 分组不存在，将模板设为未分组
            if (group == null) {
                template.setGroupId(-1);
                LogKit.info("模板关联的分组不存在，已调整为未分组: templateId=" + template.getId() +
                           ", originalGroupId=" + groupId + ", userId=" + userId);
                return RetKit.ok("adjustmentMessage", "原分组已不存在，已调整为未分组");
            }

            // 检查分组是否属于当前用户
            if (!group.getUserId().equals(userId)) {
                // 分组不属于当前用户，将模板设为未分组
                template.setGroupId(-1);
                LogKit.warn("模板关联的分组不属于当前用户，已调整为未分组: templateId=" + template.getId() +
                           ", groupId=" + groupId + ", userId=" + userId);
                return RetKit.ok("adjustmentMessage", "原分组无权限访问，已调整为未分组");
            }

            // 检查分组是否被软删除
            Date groupDeleteTime = group.get("deleteTime");
            if (groupDeleteTime != null) {
                // 分组被软删除，将模板设为未分组
                template.setGroupId(-1);
                LogKit.info("模板关联的分组已被删除，已调整为未分组: templateId=" + template.getId() +
                           ", groupId=" + groupId + ", groupName=" + group.getName() + ", userId=" + userId);
                return RetKit.ok("adjustmentMessage", "原分组\"" + group.getName() + "\"已被删除，已调整为未分组");
            }

            // 分组正常，无需调整
            return RetKit.ok();

        } catch (Exception e) {
            LogKit.error("检查模板分组状态异常: " + e.getMessage(), e);
            return RetKit.fail("检查模板分组失败: " + e.getMessage());
        }
    }

    /**
     * 构建权限信息
     */
    private Map<String, Object> buildPermissionInfo(DataRecoveryPermissionResult permissionResult) {
        Map<String, Object> permissionInfo = new HashMap<>();
        permissionInfo.put("hasPermission", permissionResult.isHasPermission());
        permissionInfo.put("userTier", permissionResult.getUserTier() != null ? permissionResult.getUserTier().name() : "FREE");
        permissionInfo.put("userTierDisplay", permissionResult.getUserTier() != null ? permissionResult.getUserTier().getDisplayName() : "免费用户");
        permissionInfo.put("recoveryPeriodDays", permissionResult.getRecoveryPeriodDays());
        permissionInfo.put("earliestRecoveryDate", permissionResult.getEarliestRecoveryDate());
        permissionInfo.put("upgradeMessage", permissionResult.getUpgradeMessage());
        permissionInfo.put("upgradeRequired", permissionResult.isUpgradeRequired());
        return permissionInfo;
    }
}
