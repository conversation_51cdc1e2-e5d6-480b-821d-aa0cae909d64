package com.sandu.xinye.api.v2.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.SqlPara;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.AESKit;
import com.sandu.xinye.common.kit.RegExpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.*;
import com.xiaoleilu.hutool.json.JSONUtil;
import com.xiaoleilu.hutool.util.ArrayUtil;
import com.xiaoleilu.hutool.util.BeanUtil;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import org.apache.http.util.TextUtils;
import org.eclipse.jetty.util.StringUtil;

import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_OPEN;

public class TempletService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(TempletService.class);

    public static final TempletService me = new TempletService();
    private static final String FILE_PATH = "templet";
    private static final Integer MAX_TEMPLET_HISTORY_COUNT = 6;

    public RetKit getTempletPage(int pageNumber, int pageSize, String name, Integer userId, String groupId,
                                 String widthRange) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        Kv kvParams = Kv.by("userId", userId).set("groupId", groupId).set("name", name);
        if (StrKit.notBlank(widthRange)) {
            String[] indexRangeArr = widthRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }

        try {
            SqlPara sqlPara = Db.getSqlPara("app.templet.paginate", kvParams);
            Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }

    public RetKit getTempletPageNoCount(int pageNumber, int pageSize, String name, Integer userId, String groupId,
                                        String widthRange) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        Kv kvParams = Kv.by("userId", userId).set("groupId", groupId).set("name", name);
        if (StrKit.notBlank(widthRange)) {
            String[] indexRangeArr = widthRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }
        SqlPara sqlPara = Db.getSqlPara("app.templet.paginate", kvParams);
        // 手动拼接分页
        sqlPara.setSql(sqlPara.getSql() + " limit ? offset ?");
        sqlPara.addPara(pageSize);
        sqlPara.addPara((pageNumber - 1) * pageSize);
        List<Record> list = Db.find(sqlPara);
        // 是否最后一页，通过当前页的数量是否小于pageSize判断
        Boolean lastPage = false;
        if (list.size() < pageSize) {
            lastPage = true;
        }
        SimplePage<Record> simplePage = new com.sandu.xinye.common.model.SimplePage<>(
                list, lastPage);
        return RetKit.ok("page", simplePage);
    }

    public RetKit addTemplet(Integer groupId, String name, String cover, String gap, Integer height, Integer width,
                             Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId,
                             String blackLabelGap,
                             String blackLabelOffset, Integer cutAfterPrint, Integer labelNum, String labelGap, Integer multiLabelType,
                             Integer paperTearType,
                             boolean autoRename, boolean canDuplicate, Integer labelType) {
        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        Float blabelgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        // 多排标签间距
        if (StrKit.notBlank(labelGap)) {
            try {
                blabelgap = Float.parseFloat(labelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查多排标签间距数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }

        name = name.trim();
        if (!canDuplicate && isExistTemplet(name, userId)) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.TEMPLET_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        return addTemplet(groupId, RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)),
                                cover, gap, height, width,
                                printDirection, paperType, machineType, data, userId, blackLabelGap, blackLabelOffset,
                                cutAfterPrint, labelNum, labelGap,
                                multiLabelType, paperTearType, true, false, labelType);
                    } catch (Exception e) {
                        return RetKit.fail("新增失败");
                    }
                } else if (name.equals(Constant.TEMPLET_DEFAULT_NAME)) {
                    return addTemplet(groupId, String.format("%s(1)", name), cover, gap, height, width,
                            printDirection, paperType, machineType, data, userId, blackLabelGap, blackLabelOffset,
                            cutAfterPrint, labelNum, labelGap,
                            multiLabelType, paperTearType, true, false, labelType);
                } else {
                    return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
                }
            } else {
                return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
            }
        }

        String newUrl = cover;
        if (!cover.startsWith("http")) {
            // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        Templet model = new Templet();
        model.setUserId(userId).setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data)
                .setCreateTime(new Date())
                .setBlackLabelGap(blg).setGroupId(groupId).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setLabelNum(labelNum).setLabelGap(blabelgap)
                .setMultiLabelType(multiLabelType)
                .setLabelType(labelType)
                .setPaperTearType(paperTearType)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW);
        boolean succ = false;
        try {
            succ = model.save();
            if (succ) {
                generateHistoryData(model.getId(), userId);

                // 如果历史记录超过6条，删除多余记录
                deleteOldestPrintHistory(userId);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return succ ? RetKit.ok().set("data", model.getId()) : RetKit.fail();
    }

    /**
     * @param model
     * @param autoRename 如果名称重复，是否自动重命名
     * @return
     */
    public RetKit addTemplet(Templet model, boolean autoRename) {
        Integer paperType = model.getPaperType();
        String name = model.getName();
        Integer userId = model.getUserId();
        String cover = model.getCover();
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }

        name = name.trim();
        if (isExistTemplet(name, userId)) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.TEMPLET_USERDEF_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        model.setName(RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)));
                        return addTemplet(model, autoRename);
                    } catch (Exception e) {
                    }
                } else {
                    model.setName(String.format("%s_副本(1)", name));
                    return addTemplet(model, autoRename);
                }
            } else {
                return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
            }
        }

        String newUrl = cover;
        if (!cover.startsWith("http")) {
            // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        model.setCover(newUrl).setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW).setCreateTime(new Date());
        boolean succ = false;
        try {
            succ = model.save();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return succ ? RetKit.ok("data", model.getId()) : RetKit.fail();
    }

    public RetKit updateTemplet(String templetId, String name, String cover, String gap, Integer height, Integer width,
                                Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId,
                                String blackLabelGap,
                                String blackLabelOffset, Integer cutAfterPrint, String labelGap, Integer multiLabelType,
                                Integer paperTearType, Boolean canDuplicate, Integer labelType, String historyId) {

        // 是否处于安全模式下
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ",
                userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }
        if (StrKit.isBlank(templetId)) {
            return RetKit.fail("模板ID是必传的！");
        }

        Templet model = Templet.dao.findById(templetId);
        if (model == null) {
            logger.error("updateTemplet=======" + "templateId :" + templetId + "  user :" + userId);
            return RetKit.fail("模板不存在！");
        }
        if (model.getType().equals(Constant.TEMPLET_TYPE_BUSINESS)) {
            return RetKit.fail("行业模板不可编辑！");
        }
        if (!model.getUserId().equals(userId)) {
            // 不属于自己的模板，不能直接编辑保存，只能新增
            // 用于4.1.10及之前的版本，分享模板的情形
            return addTemplet(-1, name, cover, gap, height, width,
                    printDirection, paperType, machineType, data, userId, blackLabelGap,
                    blackLabelOffset, cutAfterPrint, model.getLabelNum(), labelGap,
                    multiLabelType, paperTearType, false, canDuplicate, labelType);
        }

        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        Float blabelgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        // 多排标签间距
        if (StrKit.notBlank(labelGap)) {
            try {
                blabelgap = Float.parseFloat(labelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查多排标签间距数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }
        if (!canDuplicate && !name.equals(model.getName()) && isExistTemplet(name, userId)) {
            return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
        }
        String newUrl = cover;
        if (!cover.startsWith("http")) { // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }
        model.setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data)
                .setBlackLabelGap(blg).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                .setLabelGap(blabelgap)
                .setLabelType(labelType)
                .setMultiLabelType(multiLabelType)
                .setPaperTearType(paperTearType)
                .setUpdateTime(new Date());
        boolean succ = model.update();
        if (succ) {
            // 1.0 更新历史记录
            // 判断这个模板 是否有历史记录模板 有的话 更新这个历史记录
            TempletHistory historyModel = TempletHistory.dao
                    .findFirst("select * from templet_history where templetId=? ", templetId);
            if (historyModel != null) {
                // 去更新这个历史记录
                historyModel.setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                        .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection)
                        .setData(data)
                        .setBlackLabelGap(blg).setBlackLabelOffset(blo)
                        .setCutAfterPrint(cutAfterPrint)
                        .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                        .setLabelGap(blabelgap)
                        .setLabelType(labelType)
                        .setMultiLabelType(multiLabelType)
                        .setPaperTearType(paperTearType)
                        .setUpdateTime(new Date());
                historyModel.update();
            } else {
                generateHistoryData(model.getId(), userId);
            }

            // 2.0 如果历史记录超过6条，删除多余记录
            deleteOldestPrintHistory(userId);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    /**
     * 判断这个历史记录是否还存在
     *
     * @param id
     * @return
     */
    public boolean isExistHistoryTemplet(String id) {
        TempletHistory exist = TempletHistory.dao.findFirst("select * from templet_history where id=? ", id);
        return exist != null ? true : false;
    }

    /**
     * 删除超过6条记录的历史数据
     *
     * @param userId
     * @return
     */
    public void deleteOldestPrintHistory(Integer userId) {
        Db.update("DELETE FROM templet_history WHERE userId = ? and id NOT IN (" +
                        "SELECT id FROM (SELECT id FROM templet_history WHERE userId = ? ORDER BY createTime DESC LIMIT ?) AS subquery)",
                userId, userId, MAX_TEMPLET_HISTORY_COUNT);
    }

    public RetKit copyTemplet(String templetId, Integer userId) {
        if (StrKit.isBlank(templetId)) {
            return RetKit.fail("模板ID是必传的！");
        }
        Templet model = Templet.dao.findById(templetId);

        String copyName = getNoConflictTempletName(userId, model.getName());
        model.setId(null).setName(copyName)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                .setCreateTime(new Date());
        boolean succ = model.save();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    /**
     * 生成一份最新的模板历史数据
     *
     * @param templetId
     * @return
     */
    private void generateHistoryData(Integer templetId, Integer userId) {
        if (templetId == null || templetId == 0) {
            return;
        }

        Templet model = Templet.dao.findById(templetId);
        TempletHistory templetHistory = new TempletHistory();
        templetHistory.setTempletId(templetId)
                .setUserId(userId)
                .setGroupId(model.getGroupId()).setName(model.getName()).setCover(model.getCover())
                .setGap(model.getGap()).setHeight(model.getHeight()).setWidth(model.getWidth())
                .setPaperType(model.getPaperType()).setPrintDirection(model.getPrintDirection())
                .setData(model.getData())
                .setLabelType(model.getLabelType())
                .setBlackLabelGap(model.getBlackLabelGap()).setBlackLabelOffset(model.getBlackLabelOffset())
                .setType(model.getType()).setNameEn(model.getNameEn()).setNameKor(model.getNameKor())
                .setMachineType(model.getMachineType()).setCutAfterPrint(model.getCutAfterPrint())
                .setLabelNum(model.getLabelNum()).setLabelGap(model.getLabelGap()).setShareUser(model.getShareUser())
                .setMultiLabelType(model.getMultiLabelType()).setPaperTearType(model.getPaperTearType())
                .setCreateTime(new Date());
        try {
            templetHistory.save();
        } catch (Exception ex) {
            logger.error(ex.getMessage());
        }
    }

    private String getNoConflictTempletName(Integer userId, String templetName) {
        if (isExistTemplet(templetName, userId)) {
            Matcher matcher = RegExpKit.matchRegExp(templetName, RegExpKit.TEMPLET_COPY_RENAME_REGEXP);
            if (matcher != null) {
                try {
                    String needReplaceStr = matcher.group(1);
                    Integer number = Integer.valueOf(matcher.group(2));
                    return getNoConflictTempletName(userId,
                            templetName.replace(needReplaceStr, "(" + (number + 1) + ")"));
                } catch (Exception e) {
                    return null;
                }
            } else {
                return getNoConflictTempletName(userId, String.format("%s(1)", templetName));
            }
        }
        return templetName;
    }

    public RetKit remove(String id, Integer userId) {
        // 是否处于安全模式下
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ",
                userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }

        // 使用软删除服务
        return com.sandu.xinye.common.service.SoftDeleteService.me.softDeleteTemplate(Integer.valueOf(id), userId);
    }

    public RetKit rename(Long id, String newName, Integer userId) {
        if (StrKit.isBlank(newName)) {
            return RetKit.fail("模板名称不能为空！");
        }
        Templet model = Templet.dao.findById(id);
        if (model == null) {
            return RetKit.fail("找不到该模板");
        }
        if (!model.getUserId().equals(userId)) {
            return RetKit.fail("该模板不属于您，不可以重命名！");
        }
        if (!newName.equals(model.getName()) && isExistTemplet(newName, userId)) {
            return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
        }

        model.setName(newName);
        model.setUpdateTime(new Date());
        boolean succ = model.update();

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit batchRemove(String ids, String groupIds, Integer userId) {
        if (StrKit.isBlank(ids) && StrKit.isBlank(groupIds)) {
            return RetKit.fail("请选择要删除的模板！");
        }

        // 是否处于安全模式下
        RetKit safeModeCheckResult = checkSafeMode(userId);
        if (Objects.nonNull(safeModeCheckResult)) {
            return safeModeCheckResult;
        }

        // 用于收集错误信息
        final StringBuilder errorMessages = new StringBuilder();

        // 使用软删除服务进行批量删除
        boolean success = Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                // 软删除分组
                if (!StrKit.isBlank(groupIds)) {
                    String[] groupIdArray = groupIds.split(",");
                    for (String groupId : groupIdArray) {
                        RetKit result = com.sandu.xinye.common.service.SoftDeleteService.me
                            .softDeleteTemplateGroup(Integer.valueOf(groupId.trim()), userId);
                        if (!result.success()) {
                            String errorMsg = "批量删除分组失败: groupId=" + groupId + ", error=" + result.getMsg();
                            logger.warn(errorMsg);
                            if (errorMessages.length() > 0) {
                                errorMessages.append("; ");
                            }
                            errorMessages.append(result.getMsg());
                            return false;
                        }
                    }
                }

                // 软删除模板
                if (!StrKit.isBlank(ids)) {
                    String[] idArray = ids.split(",");
                    List<Integer> templateIds = new ArrayList<>();
                    for (String id : idArray) {
                        templateIds.add(Integer.valueOf(id.trim()));
                    }

                    RetKit result = com.sandu.xinye.common.service.SoftDeleteService.me
                        .batchSoftDeleteTemplates(templateIds, userId);
                    if (!result.success()) {
                        String errorMsg = "批量删除模板失败: " + result.getMsg();
                        logger.warn(errorMsg);
                        if (errorMessages.length() > 0) {
                            errorMessages.append("; ");
                        }
                        errorMessages.append(result.getMsg());
                        return false;
                    }
                }

                return true;
            }
        });

        // 根据事务结果和错误信息返回相应的 RetKit
        if (success) {
            return RetKit.ok();
        } else {
            String finalErrorMsg = errorMessages.length() > 0 ? errorMessages.toString() : "批量删除失败";
            return RetKit.fail(finalErrorMsg);
        }
    }

    public RetKit removeHistory(Long historyId, Integer userId) {
        TempletHistory model = TempletHistory.dao.findById(historyId);
        if (model == null) {
            return RetKit.fail("找不到该历史记录");
        }
        if (!model.getUserId().equals(userId)) {
            return RetKit.fail("该历史记录不属于您，不可以删除！");
        }

        // 是否处于安全模式下
        RetKit safeModeCheckResult = checkSafeMode(userId);
        if (Objects.nonNull(safeModeCheckResult)) {
            return safeModeCheckResult;
        }

        boolean succ = model.delete();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit addGroup(User user, String name, Boolean autoRename, Boolean canDuplicate) {
        name = name.trim();
        if (!canDuplicate && isExistGroup(name, user.getUserId())) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.GROUP_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        return addGroup(user, RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)), true,
                                false);
                    } catch (Exception e) {
                        return RetKit.fail("新增失败");
                    }
                } else {
                    return addGroup(user, String.format("%s(1)", name), true, false);
                }
            } else {
                return RetKit.fail(RetConstant.CODE_GROUP_REPEAT, "该分组名称已存在！");
            }

        }
        TempletGroup model = new TempletGroup();
        model.setName(name);
        model.setUserId(user.getUserId());
        model.setCreateTime(new Date());
        // 自定义模板分组
        model.setType(Constant.TEMPLET_GROUP_TYPE_SELF_DEF_NEW);

        List<TempletGroup> list = new ArrayList<TempletGroup>();
        boolean succ = model.save();
        if (succ) {
            list = getGroupListByUserId(user.getUserId());
        }
        return succ ? RetKit.ok().set("list", list) : RetKit.fail();
    }

    public RetKit getGroupList(User user) {
        List<TempletGroup> list = getGroupListByUserId(user.getUserId());
        return RetKit.ok("list", list);
    }

    public RetKit moveTempletToGroup(String templetId, String groupId, User user) {
        Templet model = Templet.dao.findById(templetId);
        if (model == null) {
            return RetKit.fail("模板id有误！");
        }
        if (model.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("模板不属于此用户！");
        }
        TempletGroup group = TempletGroup.dao.findById(groupId);
        if (group == null) {
            return RetKit.fail("分组id有误！");
        }
        if (group.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("分组不属于此用户！");
        }
        boolean succ = model.setGroupId(Integer.valueOf(groupId)).update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit moveTempletsToGroup(String templetIds, String groupId, User user) {
        if (StrKit.isBlank(templetIds) || StrKit.isBlank(groupId)) {
            return RetKit.fail("参数有误！");
        }

        // 0.0 查询是否有不属于用户的模板
        if (!StrKit.isBlank(templetIds)) {
            List<Templet> notMyTemplets = Templet.dao
                    .find("select * from templet where id in (" + templetIds + ") and userId != " + user.getUserId());
            if (CollectionUtil.isNotEmpty(notMyTemplets)) {
                return RetKit.fail("存在模板不属于您，不可移动到新分组！");
            }
        }

        String[] templetIdArray = templetIds.split(",");
        List<String> sqlParas = new ArrayList<>();
        sqlParas.add(groupId);
        sqlParas.add(user.getUserId().toString());
        List<String> templetIdList = Arrays.asList(templetIdArray);
        sqlParas.addAll(templetIdList);
        String inClause = String.join(",", Collections.nCopies(templetIdArray.length, "?"));
        String sql = "update templet set groupId = ?  WHERE userId = ? AND id IN (" + inClause + ")";
        try {
            Db.update(sql, sqlParas.toArray());
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            return RetKit.fail("修改分组失败！");
        }

        return RetKit.ok(200, "修改分组成功！");
    }

    public RetKit updateTempletGroupName(String id, String name, Boolean canDuplicate) {
        TempletGroup target = TempletGroup.dao.findById(id);
        if (target == null) {
            return RetKit.fail("参数有误！");
        }
        if (!canDuplicate && isExistGroupById(name.trim(), target.getUserId(), target.getId())) {
            return RetKit.fail(RetConstant.CODE_GROUP_REPEAT, "该分组名称已存在！");
        }
        boolean succ = target.setName(name.trim()).update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit deleteTempletGroup(String groupId, User user) {
        // 使用软删除服务
        RetKit result = com.sandu.xinye.common.service.SoftDeleteService.me
            .softDeleteTemplateGroup(Integer.valueOf(groupId), user.getUserId());

        if (result.success()) {
            // 删除成功后返回更新的分组列表
            List<TempletGroup> list = getGroupListByUserId(user.getUserId());
            return RetKit.ok().set("list", list);
        } else {
            return result;
        }
    }

    public RetKit getTemplateShareSession(long templateId, User user) {
        Templet model = Templet.dao.findById(templateId);
        if (model == null) {
            return RetKit.fail("参数有误！");
        }
        // if (model.getType() != Constant.TEMPLET_TYPE_BUSINESS &&
        // model.getUserId().intValue() != user.getUserId().intValue()) {
        // return RetKit.fail("当前模板不属于此用户！");
        // }

        String session = getRandomCode(20);
        CacheKit.put(CacheConstant.TEMPLET_SHARE, session, templateId);
        logger.info("获取分享口令, session:" + session);

        return RetKit.ok().set("data", session);
    }

    public RetKit getBatchTemplateShareSession(String templateIds, String groupIds, User user) {
        if (StrKit.isBlank(templateIds) && StrKit.isBlank(groupIds)) {
            return RetKit.fail("参数有误！");
        }

        // 0.0 查询是否有不属于用户的模板
        if (!StrKit.isBlank(templateIds)) {
            List<Templet> notMyTemplets = Templet.dao
                    .find("select * from templet where id in (" + templateIds + ") and userId != " + user.getUserId());
            if (CollectionUtil.isNotEmpty(notMyTemplets)) {
                return RetKit.fail("存在待分享的模板不属于您，不可分享！");
            }
        }
        // 0.1 查询是否有不属于用户的分组
        if (!StrKit.isBlank(groupIds)) {
            List<TempletGroup> notMyGroups = TempletGroup.dao.find(
                    "select * from templet_group where id in (" + groupIds + ") and userId != " + user.getUserId());
            if (CollectionUtil.isNotEmpty(notMyGroups)) {
                return RetKit.fail("存在待分享的分组不属于您，不可分享！");
            }
        }

        // 1. 聚合模板ID
        List<String> templateIdList = new ArrayList<>();
        if (!StrKit.isBlank(templateIds)) {
            templateIdList.addAll(Arrays.asList(templateIds.split(",")));
        }

        // 1.0 获取分组下的模板ID
        if (!StrKit.isBlank(groupIds)) {
            String[] groupIdArray = groupIds.split(",");
            List<String> sqlParas = new ArrayList<>();
            sqlParas.add(user.getUserId().toString());
            List<String> groupIdList = Arrays.asList(groupIds.split(","));
            sqlParas.addAll(groupIdList);
            String inClause = String.join(",", Collections.nCopies(groupIdArray.length, "?"));
            String sql = "SELECT id FROM templet WHERE userId=? AND groupId IN (" + inClause + ")";
            List<Integer> templetIdsOfGroupIds = Db.query(sql, sqlParas.toArray());
            if (CollectionUtil.isNotEmpty(templetIdsOfGroupIds)) {
                templateIdList.addAll(
                        templetIdsOfGroupIds.stream().map(groupId -> groupId.toString()).collect(Collectors.toList()));
            }
        }

        // 校验
        if (templateIdList.size() == 0) {
            return RetKit.fail("文件夹内没有模板！");
        }

        // 分享的总模板数量不能超过20个
        if (templateIdList.size() > Constant.TEMPLET_SHARE_MAX) {
            return RetKit.fail(RetConstant.MESSAGE_TEMPLET_SHARE_LIMIT);
        }

        String session = getRandomCode(20);
        // templateIdList 转成字符串，逗号分隔
        CacheKit.put(CacheConstant.TEMPLET_SHARE, session, String.join(",", templateIdList));
        logger.info("获取分享口令, session:" + session);

        return RetKit.ok().set("data", session);
    }

    public RetKit getTemplateByShareSession(String session, User user) {
        Object templateId = CacheKit.get(CacheConstant.TEMPLET_SHARE, session);
        if (templateId instanceof Templet) {
            return RetKit.ok().set("data", templateId).set("type", 0);
        }
        if (templateId == null) {
            return RetKit.fail("session不存在或已失效！");
        }
        String[] templateIdArray = templateId.toString().split(",");
        if (templateIdArray.length == 1) {
            Templet model = Templet.dao.findById(templateId);
            if (model == null) {
                logger.error("getTemplateByShareSession=======" + "templateId :" + templateId + "  user :"
                        + user.getUserId());
                return RetKit.fail("模板不存在！");
            }
            if (model.getUserId().intValue() == user.getUserId().intValue()) {
                return RetKit.fail("模板不能分享给自己！");
            }
            return RetKit.ok().set("data", model).set("type", 0);
        } else {
            // 查询第一个真实存在的模板
            Templet model = Templet.dao
                    .findFirst("select * from templet where id in (" + String.join(",", templateIdArray) + ")");
            if (model == null) {
                return RetKit.fail("模板不存在！");
            }
            if (model != null && model.getUserId().intValue() == user.getUserId().intValue()) {
                return RetKit.fail("模板不能分享给自己！");
            }
            return RetKit.ok().set("data", null).set("type", 1);
        }

    }

    public RetKit getTemplateShareSessionByTemplet(Templet templet) {
        String session = getRandomCode(20);
        CacheKit.put(CacheConstant.TEMPLET_SHARE, session, templet);
        logger.info("获取分享口令, session:" + session);

        return RetKit.ok().set("data", session);
    }

    public RetKit getTemplateByEancode(String eancode) {

        if (StrUtil.isEmpty(eancode)) {
            return RetKit.fail("69码不能为空！");
        }
        Templet model = Templet.dao.findFirst("select * from templet where eancode  like ? and type=?",
                "%" + eancode + "%", 1);

        return RetKit.ok().set("data", model);
    }

    public boolean isExistTemplet(String name, int userId) {
        Templet exist = Templet.dao.findFirst("select * from templet where name=? and userId=? and deleteTime IS NULL", name, userId);
        return exist != null ? true : false;
    }

    public RetKit getTempletHistory(Integer userId, Integer limit) {
        List<TempletHistory> templetHistoryList = TempletHistory.dao
                .find("select * from templet_history where userId=? order by updateTime desc limit ?", userId, limit);
        return RetKit.ok().set("list", templetHistoryList);
    }

    public RetKit shareTempletByPhoneAndIdentity(Integer userId, Integer templetId, String shareToPhone,
                                                 String shareToIdentity) {
        Templet model = Templet.dao.findById(templetId);
        if (model == null) {
            logger.error("shareTempletByPhoneAndIdentity=======" + "templateId :" + templetId + "  user :" + userId);
            return RetKit.fail("模板不存在！");
        }
        if (!model.getUserId().equals(userId)) {
            return RetKit.fail("模板不属于当前用户，不可分享！");
        }

        User user = null;
        if (StrUtil.isEmpty(shareToPhone)) {
            user = User.dao.findFirst("select * from user where identity=?", shareToIdentity);
        } else {
            user = User.dao.findFirst("select * from user where userPhone=? and identity=?", shareToPhone,
                    shareToIdentity);
        }

        if (user == null) {
            return RetKit.fail("伙伴账号不存在！");
        }

        model.setId(0).setGroupId(-1).setUserId(user.getUserId())
                .setShareUser(userId)
                .setCreateTime(new Date()).setUpdateTime(new Date());
        RetKit ret = addTemplet(model, true);
        if (ret.success()) {
            return RetKit.ok("分享标签成功");
        } else {
            logger.error("模板分享失败：" + ret.get("msg"));
            return RetKit.fail("分享失败！");
        }

    }

    public RetKit batchShareTempletByIdentity(Integer userId, String templateIds, String groupIds,
                                              String shareToIdentity) {
        if (StrKit.isBlank(templateIds) && StrKit.isBlank(groupIds)) {
            return RetKit.fail("至少选一个模板进行分享！");
        }

        // 0.0 查询是否有不属于用户的模板
        if (!StrKit.isBlank(templateIds)) {
            List<Templet> notMyTemplets = Templet.dao
                    .find("select * from templet where id in (" + templateIds + ") and userId != " + userId);
            if (CollectionUtil.isNotEmpty(notMyTemplets)) {
                return RetKit.fail("存在待分享的模板不属于您，不可分享！");
            }
        }
        // 0.1 查询是否有不属于用户的分组
        if (!StrKit.isBlank(groupIds)) {
            List<TempletGroup> notMyGroups = TempletGroup.dao
                    .find("select * from templet_group where id in (" + groupIds + ") and userId != " + userId);
            if (CollectionUtil.isNotEmpty(notMyGroups)) {
                return RetKit.fail("存在待分享的分组不属于您，不可分享！");
            }
        }
        // 0.2 查询分享码所属的用户
        User shareToUser = User.dao.findFirst("select * from user where identity=?", shareToIdentity);
        if (shareToUser == null) {
            return RetKit.fail("伙伴账号不存在！");
        }

        // 1. 聚合模板ID
        List<String> templateIdList = new ArrayList<>();
        if (!StrKit.isBlank(templateIds)) {
            templateIdList.addAll(Arrays.asList(templateIds.split(",")));
        }

        // 1.0 获取分组下的模板ID
        if (!StrKit.isBlank(groupIds)) {
            String[] groupIdArray = groupIds.split(",");
            List<String> sqlParas = new ArrayList<>();
            sqlParas.add(userId.toString());
            List<String> groupIdList = Arrays.asList(groupIds.split(","));
            sqlParas.addAll(groupIdList);
            String inClause = String.join(",", Collections.nCopies(groupIdArray.length, "?"));
            String sql = "SELECT id FROM templet WHERE userId=? AND groupId IN (" + inClause + ")";
            List<Integer> templetIdsOfGroupIds = Db.query(sql, sqlParas.toArray());
            if (CollectionUtil.isNotEmpty(templetIdsOfGroupIds)) {
                templateIdList.addAll(
                        templetIdsOfGroupIds.stream().map(groupId -> groupId.toString()).collect(Collectors.toList()));
            }
        }

        // 校验
        if (templateIdList.size() == 0) {
            return RetKit.fail("文件夹内没有模板！");
        }

        // 分享的总模板数量不能超过20个
        if (templateIdList.size() > Constant.TEMPLET_SHARE_MAX) {
            return RetKit.fail(RetConstant.MESSAGE_TEMPLET_SHARE_LIMIT);
        }

        // 批量获取分享模板保存
        boolean succ = Db.tx(() -> {
            List<String> sqlParas = new ArrayList<>();
            sqlParas.add(shareToUser.getUserId().toString());
            sqlParas.add(userId.toString());

            // 批量插入
            sqlParas.addAll(templateIdList);
            String inClause = String.join(",", Collections.nCopies(templateIdList.size(), "?"));

            String sql = "insert ignore into templet(userId,groupId,shareUser,name,cover,gap,height,width,paperType,printDirection,data,blackLabelGap,blackLabelOffset"
                    +
                    ",type,machineType,cutAfterPrint,labelNum,labelGap,multiLabelType,paperTearType,labelType) " +
                    " select ?,-1,?,name,cover,gap,height,width,paperType,printDirection,data,blackLabelGap,blackLabelOffset"
                    +
                    ",type,machineType,cutAfterPrint,labelNum,labelGap,multiLabelType,paperTearType,labelType from templet where id IN ("
                    + inClause + ")";
            try {
                // 使用参数化查询执行SQL
                Db.update(sql, sqlParas.toArray());
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
                ex.printStackTrace();
                return false;
            }
            return true;
        });

        return succ ? RetKit.ok() : RetKit.fail();

    }

    /**
     * 根据分享口令保存其他伙伴分享的模板
     *
     * @param userId
     * @param session
     * @return
     */
    public RetKit saveShareTempletBySession(Integer userId, String session) {
        logger.info("用户：" + userId);
        Object templateIdObj = CacheKit.get(CacheConstant.TEMPLET_SHARE, session);
        if (templateIdObj == null) {
            return RetKit.fail("分享口令不存在或已失效！");
        }

        // 临时编辑的模板进行分享需要走新增模板的流程
        if (templateIdObj instanceof Templet) {
            Templet model = (Templet) templateIdObj;
            // 校验
            Integer paperType = model.getPaperType();
            if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                    && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
                return RetKit.fail("纸张类型参数有误！");
            }
            String name = model.getName().trim();
            String newUrl = model.getCover();
            if (!newUrl.startsWith("http")) {
                // 本地路径，转移到本地保存图片的地址
                newUrl = UploadFileMoveKit.move(model.getCover(), FILE_PATH);
            }

            model.setId(null).setUserId(userId).setName(name).setCover(newUrl)
                    .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW).setCreateTime(new Date());
            boolean succ = false;
            try {
                succ = model.save();
            } catch (Exception e) {
                logger.error(e.getMessage());
            }

            return succ ? RetKit.ok("data", model.getId()) : RetKit.fail();
        }

        // 转成string
        String templateIds = templateIdObj.toString();
        if (StrKit.isBlank(templateIds)) {
            return RetKit.fail("分享口令不存在或已失效！");
        }
        // 获取分享模板所属的用户
        Templet shareTemplet = Templet.dao.findFirst("select * from templet where id=?", templateIds.split(",")[0]);
        if (shareTemplet != null && shareTemplet.getUserId().intValue() == userId.intValue()) {
            return RetKit.fail("模板不能分享给自己！");
        }

        logger.info(
                "saveShareTempletBySession, userId:" + userId + ",session:" + session + ",templetIds:" + templateIds);
        String shareFromUser = shareTemplet.getUserId().toString();
        logger.info("shareFromUser：" + shareFromUser);
        // 分享行业模板
        if (templateIds.split(",").length == 1 && shareTemplet != null
                && shareTemplet.getType() == Constant.TEMPLET_TYPE_BUSINESS) {
            logger.info("分享了行业模板直接进行保存，模板ID:" + templateIds);
            // 行业模板封面是加密的，复制需要解密
            String newUrl = shareTemplet.getCover();
            if (newUrl.startsWith("http")) {
                // 解密decrypt
                // 获取文件地址的最后一个/
                int lastIndex = newUrl.lastIndexOf("/");
                String fileName = newUrl.substring(lastIndex + 1);
                // 解密
                String decryptFileName = AESKit.decrypt(fileName);
                // 替换文件名
                newUrl = newUrl.replace(fileName, decryptFileName);
            }
            shareTemplet.setId(null).setGroupId(-1).setUserId(userId).setCover(newUrl)
                    .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                    .setCreateTime(new Date()).setUpdateTime(new Date());
            logger.info("用户：" + userId);
            boolean succ = false;
            try {
                succ = shareTemplet.save();
            } catch (Exception e) {
                logger.error(e.getMessage());
            }

            return succ ? RetKit.ok("data", shareTemplet.getId()) : RetKit.fail();
        }

        // 批量获取分享模板保存
        boolean succ = Db.tx(() -> {
            String[] templetIdArray = templateIds.split(",");
            List<String> sqlParas = new ArrayList<>();
            sqlParas.add(userId.toString());
            sqlParas.add(shareFromUser);

            // 批量插入
            List<String> templetIdList = Arrays.asList(templateIds.split(","));
            sqlParas.addAll(templetIdList);
            String inClause = String.join(",", Collections.nCopies(templetIdArray.length, "?"));

            String sql = "insert ignore into templet(type,userId,groupId,shareUser,name,cover,gap,height,width,paperType,printDirection,data,blackLabelGap,blackLabelOffset"
                    +
                    ",machineType,cutAfterPrint,labelNum,labelGap,multiLabelType,paperTearType,labelType) " +
                    " select 2,?,-1,?,name,cover,gap,height,width,paperType,printDirection,data,blackLabelGap,blackLabelOffset"
                    +
                    ",machineType,cutAfterPrint,labelNum,labelGap,multiLabelType,paperTearType,labelType from templet where id IN ("
                    + inClause + ")";
            try {
                // 使用参数化查询执行SQL
                Db.update(sql, sqlParas.toArray());

            } catch (Exception ex) {
                System.out.println(ex.getMessage());
                ex.printStackTrace();
                return false;
            }
            return true;
        });

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit getShareTemplet(int pageNumber, int pageSize, Integer userId, int shareType) {
        Kv kvParams = Kv.by("userId", userId);
        if (shareType == Constant.SHARE_TYPE_FROM_ME) {
            // 我的分享
            kvParams.set("shareUser", userId);
        } else if (shareType == Constant.SHARE_TYPE_FROM_OTHER) {
            // 分享给我
        }

        try {
            SqlPara sqlPara = Db.getSqlPara("app.templet.share.paginate", kvParams);
            Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }

    private boolean isExistGroup(String name, int userId) {
        TempletGroup isExist = TempletGroup.dao.findFirst("select id from templet_group where name=? and userId=? and deleteTime IS NULL",
                name, userId);
        return isExist != null;
    }

    private boolean isExistGroupById(String name, int userId, int id) {
        TempletGroup isExist = TempletGroup.dao
                .findFirst("select id from templet_group where name=? and userId=? and id != ? and deleteTime IS NULL", name, userId, id);
        return isExist != null;
    }

    private List<TempletGroup> getGroupListByUserId(int userId) {
        List<TempletGroup> list = TempletGroup.dao.find("select t.groupId, t.name, t.createTime from  ( " +
                        " select t.id as groupId, t.name, t.createTime " +
                        " FROM templet_group t " +
                        " where userId = ? and deleteTime IS NULL " +
                        " union ( " +
                        "     select tt.id as groupId, tt.name, tt.createTime  " +
                        "        from templet_group tt " +
                        " inner join user uu on  uu.userId = tt.userId and uu.wxUnionId is not null " +
                        " left join user u on uu.wxUnionId = u.wxUnionId " +
                        " where u.userId = ? and tt.deleteTime IS NULL " +
                        "    ) " +
                        ") t " +
                        " order by t.groupId desc",
                userId, userId);
        return list;
    }

    private String getRandomCode(int length) {
        String Val = "";
        Random rand = new Random();
        for (int i = 0; i < length; i++) {
            String charOrNum = rand.nextInt(3) % 3 == 0 ? "char" : "num"; // 用这个来随机产生字母还是数字, 1:2的比例
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 然后字母有大小写问题
                int choice = 97; // 加上97就是小写
                Val += (char) (choice + rand.nextInt(26));
            }
            if ("num".equalsIgnoreCase(charOrNum)) {
                Val += String.valueOf(rand.nextInt(10));
            }
        }
        return Val;
    }

    // 封装检查安全模式的方法
    private RetKit checkSafeMode(Integer userId) {
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ",
                userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }
        return null;
    }
}
