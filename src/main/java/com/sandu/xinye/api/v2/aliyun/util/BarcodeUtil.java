package com.sandu.xinye.api.v2.aliyun.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class BarcodeUtil {
    private static final Logger logger = LoggerFactory.getLogger(BarcodeUtil.class);
    private static String HOST;
    private static String PATH;
    private static String METHOD;
    private static String APP_CODE;

    static {
        try {
            Prop p = PropKit.use("common_config.txt");
            String fileName = p.get("sqlConfig");
            p = p.append(fileName);
            HOST = p.get("barcode.host");
            PATH = p.get("barcode.path");
            METHOD = p.get("barcode.method");
            APP_CODE = p.get("barcode.appcode");

            logger.info("Barcode configuration loaded. HOST: {}, PATH: {}, METHOD: {}", HOST, PATH, METHOD);
            // 隐藏部分APP_CODE信息
            String maskedAppCode = APP_CODE.substring(0, 4) + "****" + APP_CODE.substring(APP_CODE.length() - 4);
            logger.info("APP_CODE loaded: {}", maskedAppCode);
        } catch (Exception e) {
            logger.error("Failed to load barcode configuration", e);
        }
    }

    public static BarcodeResponse queryBarcode(String code) {
        logger.info("调用第三方查询条形码: {}", code);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + APP_CODE);

        Map<String, String> querys = new HashMap<>();
        querys.put("code", code);

        try {
            HttpResponse response = HttpUtils.doGet(HOST, PATH, METHOD, headers, querys);
            if(response.getStatusLine().getStatusCode()==401) {
                logger.error("查询条形码:接口鉴权失败！");
                return null;
            }
            if(response.getStatusLine().getStatusCode() != 200) {
                logger.error("查询条形码[{}]时发生错误: {}", code, response.getStatusLine().getReasonPhrase());
                return null;
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            BarcodeResponse barcodeResponse = JSON.parseObject(responseBody, BarcodeResponse.class);
            return barcodeResponse;
        } catch (Exception e) {
            logger.error("查询条形码[{}]时发生错误: {}", code, e);
            return null;
        }
    }

    public static class BarcodeResponse {
        private int showapi_res_code;
        private String showapi_res_error;
        private BarcodeBody showapi_res_body;

        public int getShowapi_res_code() {
            return showapi_res_code;
        }

        public void setShowapi_res_code(int showapi_res_code) {
            this.showapi_res_code = showapi_res_code;
        }

        public String getShowapi_res_error() {
            return showapi_res_error;
        }

        public void setShowapi_res_error(String showapi_res_error) {
            this.showapi_res_error = showapi_res_error;
        }

        public BarcodeBody getShowapi_res_body() {
            return showapi_res_body;
        }

        public void setShowapi_res_body(BarcodeBody showapi_res_body) {
            this.showapi_res_body = showapi_res_body;
        }
    }

    public static class BarcodeBody {
        private String flag;
        private String remark;
        private String code;
        private String goodsName;
        private String manuName;
        private String spec;
        private String price;
        private String trademark;
        private String img;
        private String ret_code;
        private String goodsType;
        private String sptmImg;
        private String ycg;
        private String engName;
        private String note;

        // Getters and Setters
        public String getFlag() {
            return flag;
        }

        public void setFlag(String flag) {
            this.flag = flag;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getManuName() {
            return manuName;
        }

        public void setManuName(String manuName) {
            this.manuName = manuName;
        }

        public String getSpec() {
            return spec;
        }

        public void setSpec(String spec) {
            this.spec = spec;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getTrademark() {
            return trademark;
        }

        public void setTrademark(String trademark) {
            this.trademark = trademark;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getRet_code() {
            return ret_code;
        }

        public void setRet_code(String ret_code) {
            this.ret_code = ret_code;
        }

        public String getGoodsType() {
            return goodsType;
        }

        public void setGoodsType(String goodsType) {
            this.goodsType = goodsType;
        }

        public String getSptmImg() {
            return sptmImg;
        }

        public void setSptmImg(String sptmImg) {
            this.sptmImg = sptmImg;
        }

        public String getYcg() {
            return ycg;
        }

        public void setYcg(String ycg) {
            this.ycg = ycg;
        }

        public String getEngName() {
            return engName;
        }

        public void setEngName(String engName) {
            this.engName = engName;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }
    }
}