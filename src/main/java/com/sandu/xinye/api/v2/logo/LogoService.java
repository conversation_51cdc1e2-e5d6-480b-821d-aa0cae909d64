package com.sandu.xinye.api.v2.logo;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Logo;
import com.sandu.xinye.common.model.LogoChildKind;
import com.sandu.xinye.common.model.LogoKind;

import java.util.List;

public class LogoService {

    public static final LogoService me = new LogoService();

    /*
     * 语言类型
     */
    private static final int LANGUAGE_SYSTEM = 0;
    private static final int LANGUAGE_CHINESE = 1;
    private static final int LANGUAGE_ENGLISH = 2;
    private static final int LANGUAGE_TRADITION = 3;
    private static final int LANGUAGE_KOREAN = 4;

    public RetKit getLogoList(Integer language) {
        if (language != LANGUAGE_CHINESE && language != LANGUAGE_ENGLISH && language != LANGUAGE_TRADITION && language != LANGUAGE_KOREAN) {
            return RetKit.fail("语言类型传入有误！");
        }
        List<Logo> list = Logo.dao.find("select * from logo where version = 1");
        for (Logo logo : list) {
            LogoKind kind = LogoKind.dao.findById(logo.getLogoKindId());
            LogoChildKind childKind = LogoChildKind.dao.findById(logo.getLogoChildKindId());
            String groupName = "";
            if (language == LANGUAGE_TRADITION) {
                groupName = kind.getTraditionalName() + "/" + childKind.getTraditionalName();
            } else if (language == LANGUAGE_ENGLISH) {
                groupName = kind.getEnglishName() + "/" + childKind.getEnglishName();
            } else if (language == LANGUAGE_KOREAN) {
                groupName = kind.getKoreanName() + "/" + childKind.getKoreanName();
            } else {
                groupName = kind.getLogoKindName() + "/" + childKind.getLogoChildKindName();
            }
            logo.put("groupName", groupName);
            logo.remove("logoKindId", "logoChildKindId", "sysUserId", "createTime");
        }
        return RetKit.ok("items", list);
    }


    /**
     * 获取logo主类别列表
     * @param language 显示的语言类型
     * @return
     */
    public RetKit getLogoKindList(Integer language) {
        if (language != LANGUAGE_CHINESE && language != LANGUAGE_ENGLISH && language != LANGUAGE_TRADITION && language != LANGUAGE_KOREAN) {
            return RetKit.fail("语言类型传入有误！");
        }
        String queryKindName = "logoKindName";
        if (language == LANGUAGE_TRADITION) {
            queryKindName = "traditionalName";
        } else if (language == LANGUAGE_ENGLISH) {
            queryKindName = "englishName";
        } else if (language == LANGUAGE_KOREAN) {
            queryKindName = "koreanName";
        }
        List<LogoKind> list = LogoKind.dao.find(String.format("SELECT logoKindId, %s, %1$s as logoKindName from logo_kind where version = 2 order by createTime asc", queryKindName));

        return RetKit.ok("items", list);
    }

    /**
     *
     * @param pageNumber 分页
     * @param pageSize 分页
     * @param kindId 主类别
     * @param language
     * @return
     */
    public RetKit getLogoPage(int pageNumber, int pageSize,  String kindId, Integer language) {
        Kv kvParams = Kv.by("kindId", kindId).set("lang", language);

        try {
            SqlPara sqlPara = Db.getSqlPara("app.logo.paginate", kvParams);
            Page<Logo> page = Logo.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }
}
