package com.sandu.xinye.api.v2.app_config;

import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.AppConfig;

public class AppConfigService {
    public static final AppConfigService me = new AppConfigService();

    /**
     * 查询当前热更新版本号
     * GET /api/v2/app_config/hotfix
     *
     * @return RetKit，包含热更新版本号
     */
    public RetKit hotfixVersion() {
        AppConfig config = AppConfig.dao.findFirst("select * from app_config where config_key = ?", "HOTFIX");
        String hotfixVersion = null;
        if (config != null) {
            Object value = config.get("config_value");
            if (value != null && !"0".equals(value.toString())) {
                hotfixVersion = value.toString();
            }
        }
        return RetKit.ok().set("data", hotfixVersion);
    }
}