package com.sandu.xinye.api.v2.cloudfile;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RegExpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Cloudfile;
import com.sandu.xinye.common.model.User;
import com.xiaoleilu.hutool.util.ObjectUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;

public class CloudfileService {
    private static final Logger logger = Logger.getLogger(I18nInterceptor.class);
    public static final CloudfileService me = new CloudfileService();

    public RetKit getCloudfileList(Integer userId) {
        List<Cloudfile> list = getCloudfileListByUserId(userId);
        return RetKit.ok("list", list);
    }

    public RetKit uploadCloudfile(Integer userId, int type, String fileName, String url, Boolean override) {
        fileName = fileName.trim();
        if (ObjectUtil.isNull(override)) {
            if (isExistCloudfile(fileName, userId)) {
                return RetKit.fail(RetConstant.CODE_FILE_REPEAT, "该文件名称已存在！");
            }

            Cloudfile model = new Cloudfile();
            model.setType(type);
            model.setName(fileName);
            model.setUserId(userId);
            model.setUrl(url);
            boolean succ = model.save();

            List<Cloudfile> list = new ArrayList<Cloudfile>();
            if (succ) {
                list = getCloudfileListByUserId(userId);
            }
            return succ ? RetKit.ok().set("list", list) : RetKit.fail();
        } else {
            if (BooleanUtils.isTrue(override)) {
                // 获取重名文件对象
                Cloudfile model = getCloudfileByName(fileName, userId);
                model.setUrl(url);
                boolean succ = model.update();

                List<Cloudfile> list = new ArrayList<Cloudfile>();
                if (succ) {
                    list = getCloudfileListByUserId(userId);
                }
                return succ ? RetKit.ok().set("list", list) : RetKit.fail();
            } else if (BooleanUtils.isFalse(override)) {
                String noConflictFileName = getNoConflictFileName(userId, fileName);
                Cloudfile model = new Cloudfile();
                model.setType(type);
                model.setName(noConflictFileName);
                model.setUserId(userId);
                model.setUrl(url);
                boolean succ = model.save();

                List<Cloudfile> list = new ArrayList<Cloudfile>();
                if (succ) {
                    list = getCloudfileListByUserId(userId);
                }
                return succ ? RetKit.ok().set("list", list) : RetKit.fail();
            }
        }

        return RetKit.fail("请求异常！");
    }

    private String getNoConflictFileName(int userId, String fileName) {
        if (isExistCloudfile(fileName, userId)) {
            Matcher matcher = RegExpKit.matchRegExp(fileName, RegExpKit.CLOUDFILE_RENAME_REGEXP);
            if (matcher != null) {
                try {
                    String needReplaceStr = matcher.group(1);
                    Integer number = Integer.valueOf(matcher.group(2));
                    return getNoConflictFileName(userId, fileName.replace(needReplaceStr, "(" + (number + 1) + ")"));
                } catch (Exception e) {
                    LogKit.error(e.getMessage());
                    return null;
                }
            } else {
                return getNoConflictFileName(userId, String.format("%s(1)", fileName));
            }
        }
        return fileName;
    }

    public RetKit deleteCloudfile(int userId, String fileId) {
        // 使用软删除服务
        return com.sandu.xinye.common.service.SoftDeleteService.me.softDeleteCloudfile(Integer.valueOf(fileId), userId);
    }

    public RetKit deleteCloudfile(int userId, List<String> fileIds) {
        try {
            Boolean succ = Db.tx(() -> {
                for (String fileId : fileIds) {
                    RetKit result = com.sandu.xinye.common.service.SoftDeleteService.me
                        .softDeleteCloudfile(Integer.valueOf(fileId), userId);
                    if (!result.success()) {
                        logger.debug("批量删除文件失败: fileId=" + fileId + ", error=" + result.getMsg());
                        return false;
                    }
                }
                return true;
            });

            return succ ? RetKit.ok() : RetKit.fail();
        } catch (Exception ex) {
            return RetKit.fail(ex.getMessage());
        }

    }

    private Cloudfile getCloudfileByName(String name, int userId) {
        Cloudfile cloudfile = Cloudfile.dao.findFirst("select * from cloudfile where name=? and userId=? and deleteTime IS NULL",
                name, userId);
        return cloudfile;
    }

    private boolean isExistCloudfile(String name, int userId) {
        Cloudfile isExist = Cloudfile.dao.findFirst("select id from cloudfile where name=? and userId=? and deleteTime IS NULL",
                name, userId);
        return isExist != null;
    }

    private List<Cloudfile> getCloudfileListByUserId(int userId) {
        List<Cloudfile> list = Cloudfile.dao.find("select id,name,url,type,createTime from cloudfile where userId=? and deleteTime IS NULL order by createTime desc", userId);
        return list;
    }

}
