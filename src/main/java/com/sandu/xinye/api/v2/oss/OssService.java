package com.sandu.xinye.api.v2.oss;


import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.common.kit.RetKit;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

public class OssService {

    public static final OssService me = new OssService();

    private static String stsTokenServer = "http://************:7080";
    Prop p = PropKit.use("common_config.txt");

    {
        String fileName = p.get("sqlConfig");
        p.append(fileName);
        stsTokenServer = p.get("stsTokenServer");
    }

    /**
     * @return
     * @Title: getAssumeRole
     * @Description:
     * <AUTHOR>
     */
    public RetKit getAssumeRole() {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(stsTokenServer);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                System.out.println("API返回结果：" + result);
                String assumeRoleStr = result.replaceAll("\n", "");
                System.out.println("API返回结果：" + assumeRoleStr);
                JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
                if (assumeRoleObject.get("StatusCode").equals("200")) {
                    return RetKit.ok().set("data", assumeRoleObject);
                } else {
                    return RetKit.fail(assumeRoleObject.get("ErrorMessage"));
                }

            } else {
                System.out.println("API请求失败，错误码：" + statusCode);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return RetKit.fail("请求STS临时凭证失败!");
    }

}
