package com.sandu.xinye.api.v2.app_config;

import com.jfinal.aop.Clear;
import com.jfinal.core.ActionKey;
import com.sandu.xinye.common.controller.AppController;

public class AppConfigController extends AppController {
  /**
   * 查询是否开启热修复
   * GET /api/v2/app_config/hotfix
   */
  @Clear
  @ActionKey("/api/v2/app_config/hotfix")
  public void hotfixEnabled() {
    renderJson(AppConfigService.me.hotfixVersion());
  }
}