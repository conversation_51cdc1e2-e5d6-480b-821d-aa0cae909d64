package com.sandu.xinye.api.v2.printrecord.dto;

/**
 * 保存模板打印记录请求参数
 * 
 * <AUTHOR> Team
 */
public class SaveTemplatePrintRecordRequest {
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板封面
     */
    private String templateCover;
    
    /**
     * 打印宽度
     */
    private Integer printWidth;
    
    /**
     * 打印高度
     */
    private Integer printHeight;
    
    /**
     * 打印份数
     */
    private Integer printCopies;
    
    /**
     * 打印端类型
     */
    private Integer printPlatform;
    
    /**
     * 模板数据
     */
    private String templateData;

    // Getter and Setter methods
    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateCover() {
        return templateCover;
    }

    public void setTemplateCover(String templateCover) {
        this.templateCover = templateCover;
    }

    public Integer getPrintWidth() {
        return printWidth;
    }

    public void setPrintWidth(Integer printWidth) {
        this.printWidth = printWidth;
    }

    public Integer getPrintHeight() {
        return printHeight;
    }

    public void setPrintHeight(Integer printHeight) {
        this.printHeight = printHeight;
    }

    public Integer getPrintCopies() {
        return printCopies;
    }

    public void setPrintCopies(Integer printCopies) {
        this.printCopies = printCopies;
    }

    public Integer getPrintPlatform() {
        return printPlatform;
    }

    public void setPrintPlatform(Integer printPlatform) {
        this.printPlatform = printPlatform;
    }

    public String getTemplateData() {
        return templateData;
    }

    public void setTemplateData(String templateData) {
        this.templateData = templateData;
    }
}
