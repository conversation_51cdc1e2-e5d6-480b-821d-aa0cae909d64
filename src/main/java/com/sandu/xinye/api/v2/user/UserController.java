package com.sandu.xinye.api.v2.user;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.api.v2.user.validator.UpdatePwdValidator;
import com.sandu.xinye.common.annotation.OperationLog;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.api.model.XpUserPrefrence;
import com.sandu.xinye.api.v2.user.validator.UpdatePwdValidator;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.UserPreference;

import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_CLOSE;
import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_OPEN;

/**
 * <AUTHOR>
 */
public class UserController extends AppController {
    /**
     * @Title: list
     * @Description: 查找苹果用户列表
     * <AUTHOR>
     */
    @Clear
    @Before({AttackAntiInterceptor.class})
    public void appleUserList() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        RetKit ret = UserService.me.list(pageNumber, pageSize);
        renderJson(ret);
    }

    @Clear
    @Before({AttackAntiInterceptor.class})
    public void updateAppleUser() {
        String oldUserId = getPara("oldUserId");
        String newUserId = getPara("newUserId");
        RetKit ret = UserService.me.updateAppleUser(oldUserId, newUserId);
        renderJson(ret);
    }

    /**
     * 修改手机号
     */
    public void updatePhone() {
        String phone = getPara("phone");
        String captcha = getPara("captcha");
        Integer userId = getUser().getUserId();
        RetKit ret = UserService.me.updatePhone(phone, captcha, userId);
        renderJson(ret);
    }

    /**
     * 修改密码
     */
    @Before({UpdatePwdValidator.class})
    public void updatePassword() {
        String oldPassword = getPara("old_password");
        String newPassword = getPara("new_password");
        Integer userId = getUser().getUserId();
        RetKit ret = UserService.me.updatePassword(oldPassword, newPassword, userId);
        renderJson(ret);
    }

    /**
     * 绑定手机号
     */
    public void bindPhone() {
        String platform = getHeader("platform");
        String phone = getPara("phone");
        String captcha = getPara("captcha");
        Integer userId = getUser().getUserId();
        RetKit ret = UserService.me.bindPhone(platform, phone, captcha, userId);
        renderJson(ret);
    }

    /**
     * 修改昵称和头像
     */
    @OperationLog(modelName = "user")
    public void update() {
        LogKit.info("修改昵称和头像接口开始--------------------");
        String userNickName = getPara("userNickName");
        String userImg = getPara("userImg");
        RetKit ret = UserService.me.update(getUser().getUserId(), userNickName, userImg);
        renderJson(ret);
        LogKit.info("修改昵称和头像接口结束--------------------");
    }
    public void userPreference() {
        User user = getUser();
        RetKit ret = UserService.me.preference(user);
        renderJson(ret);
    }

    @Before({PostOnlyInterceptor.class})
    public void updateUserPreference(@JsonBody XpUserPrefrence preference) {
        RetKit ret = UserService.me.updatePreference(getUser(), preference);
        renderJson(ret);
    }

    /**
     * 绑定推广码接口
     *
     * POST /api/v2/user/bindPromotionCode
     * 参数：promotionCode - 推广码
     * 需要登录：是
     */
    public void bindPromotionCode() {
        LogKit.info("用户绑定推广码接口开始--------------------");

        // 获取当前登录用户
        User currentUser = getUser();
        if (currentUser == null) {
            renderJson(RetKit.fail("请先登录"));
            return;
        }

        String promotionCode = getPara("promotionCode");
        RetKit ret = UserService.me.bindPromotionCode(currentUser.getUserId(), promotionCode);
        renderJson(ret);

        LogKit.info("用户绑定推广码接口结束--------------------");
    }

    /**
     * 查询推广码绑定状态接口
     *
     * GET /api/v2/user/promotionCodeStatus
     * 需要登录：是
     */
    public void getPromotionCodeStatus() {
        LogKit.info("查询推广码绑定状态接口开始--------------------");

        // 获取当前登录用户
        User currentUser = getUser();
        if (currentUser == null) {
            renderJson(RetKit.fail("请先登录"));
            return;
        }

        RetKit ret = UserService.me.getPromotionCodeStatus(currentUser.getUserId());
        renderJson(ret);

        LogKit.info("查询推广码绑定状态接口结束--------------------");
    }

}
