package com.sandu.xinye.api.v2.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.UserGoods;

import java.util.Date;

public class UserGoodsController extends AppController {

  /**
   * 获取用户商品列表
   */
  public void list() {
    Integer userId = getUser().getUserId();
    UserGoods userGoods = UserGoods.dao.findFirst("select * from user_goods where user_id = ?", userId);

    if (userGoods == null) {
      renderJson(RetKit.ok("data", new JSONArray()));
      return;
    }

    String data = userGoods.getData();
    JSONArray goodsList = JSON.parseArray(data);
    renderJson(RetKit.ok("data", goodsList));
  }

  /**
   * 更新用户商品列表
   */
  @Before({PostOnlyInterceptor.class})
  @OperationLog(modelName = "user_goods")
  public void update() {
    Integer userId = getUser().getUserId();
    Record bodyPara = getArgsRecord();
    String goodsData = bodyPara.getStr("data");

    if (goodsData == null) {
      renderJson(RetKit.fail("商品数据不能为空"));
      return;
    }

    try {
      // 验证JSON格式
      JSON.parseArray(goodsData);
    } catch (Exception e) {
      LogKit.error("商品数据格式错误", e);
      renderJson(RetKit.fail("商品数据格式错误"));
      return;
    }

    UserGoods userGoods = UserGoods.dao.findFirst("select * from user_goods where user_id = ?", userId);
    boolean success;

    if (userGoods == null) {
      userGoods = new UserGoods();
      userGoods.setUserId(userId);
      userGoods.setData(goodsData);
      userGoods.setCreateTime(new Date());
      userGoods.setUpdateTime(new Date());
      success = userGoods.save();
    } else {
      userGoods.setData(goodsData);
      userGoods.setUpdateTime(new Date());
      success = userGoods.update();
    }

    if (success) {
      renderJson(RetKit.ok());
    } else {
      renderJson(RetKit.fail("更新失败"));
    }
  }
}