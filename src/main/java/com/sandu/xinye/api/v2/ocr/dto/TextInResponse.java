package com.sandu.xinye.api.v2.ocr.dto;

import java.util.List;
import java.util.Map;

/**
 * TextIn API响应数据传输对象
 * 基于TextIn文档解析API的实际响应格式
 * 使用Map来处理复杂的嵌套结构，避免过度复杂的类定义
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class TextInResponse {

    /**
     * 响应状态码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 识别结果数据 - 使用Map处理复杂结构
     */
    private Map<String, Object> result;

    /**
     * 每页的处理信息
     */
    private List<Map<String, Object>> metrics;

    // 主要的getter和setter方法
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getResult() {
        return result;
    }

    public void setResult(Map<String, Object> result) {
        this.result = result;
    }

    public List<Map<String, Object>> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<Map<String, Object>> metrics) {
        this.metrics = metrics;
    }

    // 便捷方法来获取result中的常用字段
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getPages() {
        if (result != null) {
            return (List<Map<String, Object>>) result.get("pages");
        }
        return null;
    }

    public String getMarkdown() {
        if (result != null) {
            return (String) result.get("markdown");
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getDetail() {
        if (result != null) {
            return (List<Map<String, Object>>) result.get("detail");
        }
        return null;
    }
}
