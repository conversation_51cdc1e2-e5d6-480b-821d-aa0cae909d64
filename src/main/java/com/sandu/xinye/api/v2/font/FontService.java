package com.sandu.xinye.api.v2.font;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Font;
import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;

import java.io.File;
import java.util.List;

public class FontService {
    public static final FontService me = new FontService();
    public RetKit getFont(String fontKind) {
        SqlPara sqlPara = Db.getSqlPara("app.font.paginate", Kv.by("fontKind", fontKind));
        List<Font> page = Font.dao.find(sqlPara);
        return RetKit.ok("list", page);
    }

    public File downloadFile(int fontId) {
        Font font = Font.dao.findById(fontId);
        if (font == null) {
            return null;
        }

        String fontUrl = font.getFontUrl();
        if (fontUrl.startsWith("http")) {
            fontUrl = fontUrl.replace( Constant.REAL_UPLOAD_IP, "/upload");
        }

        String filePath = fontUrl.replace("/upload", Constant.REAL_UPLOAD_PATH);

        File file = new File(filePath);
        if (file.exists()) {
            return file;
        } else {
            return null;
        }
    }

}
