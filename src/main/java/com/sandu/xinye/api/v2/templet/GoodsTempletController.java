package com.sandu.xinye.api.v2.templet;

import com.jfinal.aop.Before;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.api.v2.templet.validator.TempletGroupValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletShareValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletUpdateValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletValidator;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.User;

public class GoodsTempletController extends AppController {

    /**
     * @Title: getTempletPage
     * @Description: 获得模板分页
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getTempletPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String name = getPara("name");
        String widthRange = getPara("width", "");
        // 加上-1，兼容旧版本不传参数
        RetKit ret = GoodsTempletService.me.getTempletPage(pageNumber, pageSize, name, getUser().getUserId(),
                widthRange);
        renderJson(ret);
    }

    /**
     * @Title: addTemplet
     * @Description: 添加模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName = "goods_templet")
    public void addTemplet() {
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean autoRename = getParaToBoolean("autoRename", true);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // 多排标签
        Integer labelNum = getParaToInt("labelNum", 1);
        String labelGap = getPara("labelGap");
        Integer multiLabelType = getParaToInt("multiLabelType", 0);

        // labelType字段整型（标签形状） 1.矩形 2.圆角矩形 3.圆
        Integer labelType = getParaToInt("labelType", 1);
        // 模板分组
        Integer groupId = getParaToInt("groupId", -1);
        // 撕纸类型
        // 默认 1 - 撕离
        Integer paperTearType = getParaToInt("paperTearType", 1);

        RetKit ret = GoodsTempletService.me.addTemplet(groupId, name, cover, gap, height, width, printDirection,
                paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap,
                multiLabelType, paperTearType, autoRename, canDuplicate, labelType);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName = "goods_templet")
    public void updateTemplet() {
        String id = getPara("id");
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // labelType字段整型（标签形状） 1.矩形 2.圆角矩形 3.圆
        Integer labelType = getParaToInt("labelType", 1);
        // 多排标签
        String labelGap = getPara("labelGap");
        // 多排标签
        Integer multiLabelType = getParaToInt("multiLabelType", 0);
        // 撕纸类型
        // 默认 1 - 撕离
        Integer paperTearType = getParaToInt("paperTearType", 1);
        String historyId = getPara("historyId", "");

        RetKit ret = GoodsTempletService.me.updateTemplet(id, name, cover, gap, height, width, printDirection,
                paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelGap, multiLabelType,
                paperTearType, canDuplicate, labelType, historyId);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName = "goods_templet")
    public void copyTemplet() {
        String id = getPara("id");

        RetKit ret = GoodsTempletService.me.copyTemplet(id, getUser().getUserId());
        renderJson(ret);
    }

    /**
     * @Title: remove
     * @Description: 删除模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName = "goods_templet")
    public void remove() {
        String id = getPara("id");
        Integer userId = getUser().getUserId();
        RetKit ret = GoodsTempletService.me.remove(id, userId);
        renderJson(ret);
    }
    /**
     * @Description: 判断模板是否存在
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void exists() {
        String name = getPara("name");

        Boolean result = GoodsTempletService.me.isExistTemplet(name, getUser().getUserId());
        RetKit ret = RetKit.ok().set("data", result);
        renderJson(ret);
    }

    /**
     * @Title: rename
     * @Description: 重命名模板
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "goods_templet")
    public void rename() {
        Long id = getParaToLong("id");
        String newName = getPara("name");
        Integer userId = getUser().getUserId();
        RetKit ret = GoodsTempletService.me.rename(id, newName, userId);
        renderJson(ret);
    }

    /**
     * @Title: batchRemove
     * @Description: 批量删除
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "goods_templet")
    public void batchRemove() {
        String ids = getPara("ids");
        String groupIds = getPara("groupIds");
        Integer userId = getUser().getUserId();
        RetKit ret = GoodsTempletService.me.batchRemove(ids, groupIds, userId);
        renderJson(ret);
    }


}
