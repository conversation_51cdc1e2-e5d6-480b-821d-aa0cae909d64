package com.sandu.xinye.api.v2.aliyun.util;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;

public class HttpUtils {

  /**
   * 判断字符串是否为空
   * 
   * @param str 字符串
   * @return 是否为空
   */
  private static boolean isBlank(String str) {
    return str == null || str.trim().length() == 0;
  }

  /**
   * 判断字符串是否不为空
   * 
   * @param str 字符串
   * @return 是否不为空
   */
  private static boolean isNotBlank(String str) {
    return !isBlank(str);
  }

  /**
   * 执行GET请求
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doGet(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpGet request = new HttpGet(buildUrl(host, path, querys));

    // 设置默认请求头
    request.setHeader("Content-Type", "application/json");
    request.setHeader("Accept", "application/json");

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    return httpClient.execute(request);
  }

  /**
   * 执行POST请求，支持表单提交
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @param bodys   表单参数
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doPost(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys, Map<String, String> bodys) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpPost request = new HttpPost(buildUrl(host, path, querys));

    // 设置默认请求头
    request.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
    request.setHeader("Accept", "application/json");

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    // 设置表单参数
    if (bodys != null) {
      List<NameValuePair> nameValuePairList = new ArrayList<NameValuePair>();
      for (String key : bodys.keySet()) {
        nameValuePairList.add(new BasicNameValuePair(key, bodys.get(key)));
      }
      UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nameValuePairList, "UTF-8");
      request.setEntity(formEntity);
    }

    return httpClient.execute(request);
  }

  /**
   * 执行POST请求，支持JSON字符串
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @param body    JSON字符串
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doPost(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys, String body) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpPost request = new HttpPost(buildUrl(host, path, querys));

    // 设置默认请求头
    request.setHeader("Content-Type", "application/json");
    request.setHeader("Accept", "application/json");

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    // 设置请求体
    if (isNotBlank(body)) {
      request.setEntity(new StringEntity(body, "UTF-8"));
    }

    return httpClient.execute(request);
  }

  /**
   * 执行POST请求，支持字节数组
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @param body    字节数组
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doPost(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys, byte[] body) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpPost request = new HttpPost(buildUrl(host, path, querys));

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    // 设置请求体
    if (body != null) {
      request.setEntity(new ByteArrayEntity(body));
    }

    return httpClient.execute(request);
  }

  /**
   * 执行PUT请求，支持JSON字符串
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @param body    JSON字符串
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doPut(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys, String body) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpPut request = new HttpPut(buildUrl(host, path, querys));

    // 设置默认请求头
    request.setHeader("Content-Type", "application/json");
    request.setHeader("Accept", "application/json");

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    // 设置请求体
    if (isNotBlank(body)) {
      request.setEntity(new StringEntity(body, "UTF-8"));
    }

    return httpClient.execute(request);
  }

  /**
   * 执行PUT请求，支持字节数组
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @param body    字节数组
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doPut(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys, byte[] body) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpPut request = new HttpPut(buildUrl(host, path, querys));

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    // 设置请求体
    if (body != null) {
      request.setEntity(new ByteArrayEntity(body));
    }

    return httpClient.execute(request);
  }

  /**
   * 执行DELETE请求
   * 
   * @param host    主机地址
   * @param path    路径
   * @param method  方法
   * @param headers 请求头
   * @param querys  查询参数
   * @return HttpResponse
   * @throws Exception
   */
  public static HttpResponse doDelete(String host, String path, String method, Map<String, String> headers,
      Map<String, String> querys) throws Exception {
    HttpClient httpClient = wrapClient(host);
    HttpDelete request = new HttpDelete(buildUrl(host, path, querys));

    // 设置默认请求头
    request.setHeader("Content-Type", "application/json");
    request.setHeader("Accept", "application/json");

    // 设置自定义请求头
    if (headers != null) {
      for (Map.Entry<String, String> e : headers.entrySet()) {
        request.setHeader(e.getKey(), e.getValue());
      }
    }

    return httpClient.execute(request);
  }

  /**
   * 构建URL
   * 
   * @param host   主机地址
   * @param path   路径
   * @param querys 查询参数
   * @return 完整URL
   * @throws UnsupportedEncodingException
   */
  private static String buildUrl(String host, String path, Map<String, String> querys)
      throws UnsupportedEncodingException {
    StringBuilder sbUrl = new StringBuilder();
    sbUrl.append(host);

    if (!isBlank(path)) {
      sbUrl.append(path);
    }

    if (querys != null) {
      StringBuilder sbQuery = new StringBuilder();
      for (Map.Entry<String, String> query : querys.entrySet()) {
        if (0 < sbQuery.length()) {
          sbQuery.append("&");
        }
        if (isBlank(query.getKey()) && !isBlank(query.getValue())) {
          sbQuery.append(query.getValue());
        }
        if (!isBlank(query.getKey())) {
          sbQuery.append(query.getKey());
          if (!isBlank(query.getValue())) {
            sbQuery.append("=");
            sbQuery.append(URLEncoder.encode(query.getValue(), "UTF-8"));
          }
        }
      }
      if (0 < sbQuery.length()) {
        sbUrl.append("?").append(sbQuery);
      }
    }

    return sbUrl.toString();
  }

  /**
   * 包装HttpClient，支持HTTPS
   * 
   * @param host 主机地址
   * @return HttpClient
   */
  private static HttpClient wrapClient(String host) {
    HttpClient httpClient = new DefaultHttpClient();
    if (host.startsWith("https://")) {
      sslClient(httpClient);
    }
    return httpClient;
  }

  /**
   * 配置SSL客户端
   * 
   * @param httpClient HttpClient
   */
  private static void sslClient(HttpClient httpClient) {
    try {
      SSLContext ctx = SSLContext.getInstance("TLS");
      X509TrustManager tm = new X509TrustManager() {
        public X509Certificate[] getAcceptedIssuers() {
          return null;
        }

        public void checkClientTrusted(X509Certificate[] xcs, String str) {
        }

        public void checkServerTrusted(X509Certificate[] xcs, String str) {
        }
      };
      ctx.init(null, new TrustManager[] { tm }, null);
      SSLSocketFactory ssf = new SSLSocketFactory(ctx);
      ssf.setHostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
      ClientConnectionManager ccm = httpClient.getConnectionManager();
      SchemeRegistry registry = ccm.getSchemeRegistry();
      registry.register(new Scheme("https", 443, ssf));
    } catch (KeyManagementException ex) {
      throw new RuntimeException(ex);
    } catch (NoSuchAlgorithmException ex) {
      throw new RuntimeException(ex);
    }
  }
}