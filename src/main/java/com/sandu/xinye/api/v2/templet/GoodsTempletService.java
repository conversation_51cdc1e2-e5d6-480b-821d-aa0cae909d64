package com.sandu.xinye.api.v2.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.AESKit;
import com.sandu.xinye.common.kit.RegExpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.*;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_OPEN;

public class GoodsTempletService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(GoodsTempletService.class);

    public static final GoodsTempletService me = new GoodsTempletService();
    private static final String FILE_PATH = "templet";
    private static final Integer MAX_TEMPLET_HISTORY_COUNT = 6;

    public RetKit getTempletPage(int pageNumber, int pageSize, String name, Integer userId, String widthRange) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        Kv kvParams = Kv.by("userId", userId).set("name", name);
        if (StrKit.notBlank(widthRange)) {
            String[] indexRangeArr = widthRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }

        try {
            SqlPara sqlPara = Db.getSqlPara("app.goods.templet.paginate", kvParams);
            Page<GoodsTemplet> page = GoodsTemplet.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }

    public RetKit addTemplet(Integer groupId, String name, String cover, String gap, Integer height, Integer width,
                             Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId, String blackLabelGap,
                             String blackLabelOffset, Integer cutAfterPrint, Integer labelNum, String labelGap, Integer multiLabelType, Integer paperTearType,
                             boolean autoRename, boolean canDuplicate, Integer labelType) {
        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        Float blabelgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        // 多排标签间距
        if (StrKit.notBlank(labelGap)) {
            try {
                blabelgap = Float.parseFloat(labelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查多排标签间距数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }

        name = name.trim();
        if (!canDuplicate && isExistTemplet(name, userId)) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.TEMPLET_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        return addTemplet(groupId, RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)), cover, gap, height, width,
                                printDirection, paperType, machineType, data, userId, blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap,
                                multiLabelType, paperTearType, true, false, labelType);
                    } catch (Exception e) {
                        return RetKit.fail("新增失败");
                    }
                } else if (name.equals(Constant.TEMPLET_DEFAULT_NAME)) {
                    return addTemplet(groupId, String.format("%s(1)", name), cover, gap, height, width,
                            printDirection, paperType, machineType, data, userId, blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap,
                            multiLabelType, paperTearType, true, false, labelType);
                } else {
                    return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
                }
            } else {
                return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
            }
        }

        String newUrl = cover;
        if (!cover.startsWith("http")) {
            // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        GoodsTemplet model = new GoodsTemplet();
        model.setUserId(userId).setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data).setCreateTime(new Date())
                .setBlackLabelGap(blg).setGroupId(groupId).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setLabelNum(labelNum).setLabelGap(blabelgap)
                .setMultiLabelType(multiLabelType)
                .setLabelType(labelType)
                .setPaperTearType(paperTearType)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW);
        boolean succ = false;
        try {
            succ = model.save();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return succ ? RetKit.ok().set("data", model.getId()) : RetKit.fail();
    }

    /**
     * @param model
     * @param autoRename 如果名称重复，是否自动重命名
     * @return
     */
    public RetKit addTemplet(GoodsTemplet model, boolean autoRename) {
        Integer paperType = model.getPaperType();
        String name = model.getName();
        Integer userId = model.getUserId();
        String cover = model.getCover();
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }

        name = name.trim();
        if (isExistTemplet(name, userId)) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.TEMPLET_USERDEF_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        model.setName(RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)));
                        return addTemplet(model, autoRename);
                    } catch (Exception e) {
                    }
                } else {
                    model.setName(String.format("%s_副本(1)", name));
                    return addTemplet(model, autoRename);
                }
            } else {
                return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
            }
        }

        String newUrl = cover;
        if (!cover.startsWith("http")) {
            // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        model.setCover(newUrl).setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW).setCreateTime(new Date());
        boolean succ = false;
        try {
            succ = model.save();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return succ ? RetKit.ok("data", model.getId()) : RetKit.fail();
    }

    public RetKit updateTemplet(String templetId, String name, String cover, String gap, Integer height, Integer width,
                                Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId, String blackLabelGap,
                                String blackLabelOffset, Integer cutAfterPrint, String labelGap, Integer multiLabelType,
                                Integer paperTearType, Boolean canDuplicate, Integer labelType, String historyId) {

        //是否处于安全模式下
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }
        if (StrKit.isBlank(templetId)) {
            return RetKit.fail("模板ID是必传的！");
        }

        GoodsTemplet model = GoodsTemplet.dao.findById(templetId);
        if (model == null) {
            logger.error("updateTemplet=======" + "templateId :" + templetId + "  user :" + userId);
            return RetKit.fail("模板不存在！");
        }
        if (model.getType().equals(Constant.TEMPLET_TYPE_BUSINESS)) {
            return RetKit.fail("行业模板不可编辑！");
        }
        if (!model.getUserId().equals(userId)) {
            // 不属于自己的模板，不能直接编辑保存，只能新增
            // 用于4.1.10及之前的版本，分享模板的情形
            return addTemplet(-1, name, cover, gap, height, width,
                    printDirection, paperType, machineType, data, userId, blackLabelGap,
                    blackLabelOffset, cutAfterPrint, model.getLabelNum(), labelGap,
                    multiLabelType, paperTearType, false, canDuplicate, labelType);
        }

        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        Float blabelgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        // 多排标签间距
        if (StrKit.notBlank(labelGap)) {
            try {
                blabelgap = Float.parseFloat(labelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查多排标签间距数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }
        if (!canDuplicate && !name.equals(model.getName()) && isExistTemplet(name, userId)) {
            return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
        }
        String newUrl = cover;
        if (!cover.startsWith("http")) {  // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }
        model.setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data)
                .setBlackLabelGap(blg).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                .setLabelGap(blabelgap)
                .setLabelType(labelType)
                .setMultiLabelType(multiLabelType)
                .setPaperTearType(paperTearType)
                .setUpdateTime(new Date());
        boolean succ = model.update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit copyTemplet(String templetId, Integer userId) {
        if (StrKit.isBlank(templetId)) {
            return RetKit.fail("模板ID是必传的！");
        }
        GoodsTemplet model = GoodsTemplet.dao.findById(templetId);

        String copyName = getNoConflictTempletName(userId, model.getName());
        model.setId(null).setName(copyName)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                .setCreateTime(new Date());
        boolean succ = model.save();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    private String getNoConflictTempletName(Integer userId, String templetName) {
        if (isExistTemplet(templetName, userId)) {
            Matcher matcher = RegExpKit.matchRegExp(templetName, RegExpKit.TEMPLET_COPY_RENAME_REGEXP);
            if (matcher != null) {
                try {
                    String needReplaceStr = matcher.group(1);
                    Integer number = Integer.valueOf(matcher.group(2));
                    return getNoConflictTempletName(userId, templetName.replace(needReplaceStr, "(" + (number + 1) + ")"));
                } catch (Exception e) {
                    return null;
                }
            } else {
                return getNoConflictTempletName(userId, String.format("%s(1)", templetName));
            }
        }
        return templetName;
    }

    public RetKit remove(String id, Integer userId) {
        GoodsTemplet model = GoodsTemplet.dao.findById(id);
        if (model == null) {
            return RetKit.fail("找不到该模板");
        }
        if (!model.getUserId().equals(userId)) {
            return RetKit.fail("该模板不属于您，不可以删除！");
        }
        //是否处于安全模式下
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }
        boolean succ = GoodsTemplet.dao.deleteById(id);
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit rename(Long id, String newName, Integer userId) {
        if (StrKit.isBlank(newName)) {
            return RetKit.fail("模板名称不能为空！");
        }
        GoodsTemplet model = GoodsTemplet.dao.findById(id);
        if (model == null) {
            return RetKit.fail("找不到该模板");
        }
        if (!model.getUserId().equals(userId)) {
            return RetKit.fail("该模板不属于您，不可以重命名！");
        }
        if (!newName.equals(model.getName()) && isExistTemplet(newName, userId)) {
            return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
        }

        model.setName(newName);
        model.setUpdateTime(new Date());
        boolean succ = model.update();

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit batchRemove(String ids, String groupIds, Integer userId) {
        if (StrKit.isBlank(ids) && StrKit.isBlank(groupIds)) {
            return RetKit.fail("请选择要删除的模板！");
        }

        //批量删除
        boolean succ = Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                //删除模板
                if (!StrKit.isBlank(ids)) {
                    String[] idArray = ids.split(",");
                    List<String> sqlParas = new ArrayList<>();
                    sqlParas.add(userId.toString());

                    List<String> idList = Arrays.asList(ids.split(","));
                    sqlParas.addAll(idList);

                    //删除模板
                    String inClause = String.join(",", Collections.nCopies(idArray.length, "?"));
                    String sql = "DELETE FROM goods_templet WHERE userId=? AND id IN (" + inClause + ")";
                    // 使用参数化查询执行SQL
                    Db.update(sql, sqlParas.toArray());
                }

                return true;
            }
        });

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public boolean isExistTemplet(String name, int userId) {
        GoodsTemplet exist = GoodsTemplet.dao.findFirst("select * from goods_templet where name=? and userId=?", name, userId);
        return exist != null ? true : false;
    }

    private String getRandomCode(int length) {
        String Val = "";
        Random rand = new Random();
        for (int i = 0; i < length; i++) {
            String charOrNum = rand.nextInt(3) % 3 == 0 ? "char" : "num";  //用这个来随机产生字母还是数字, 1:2的比例
            if ("char".equalsIgnoreCase(charOrNum)) {
                //然后字母有大小写问题
                int choice = 97;    //加上97就是小写
                Val += (char) (choice + rand.nextInt(26));
            }
            if ("num".equalsIgnoreCase(charOrNum)) {
                Val += String.valueOf(rand.nextInt(10));
            }
        }
        return Val;
    }

    // 封装检查安全模式的方法
    private RetKit checkSafeMode(Integer userId) {
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", userId);
        if (preference != null && preference.getSafeMode() == SAFE_MODE_OPEN) {
            return RetKit.fail("您当前处于安全模式,请先前往设置关闭安全模式");
        }
        return null;
    }
}
