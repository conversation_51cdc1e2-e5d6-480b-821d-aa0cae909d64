package com.sandu.xinye.api.v2.operate;

import com.jfinal.kit.PropKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.model.OperationLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * 操作日志记录接口
 * <AUTHOR>
 */
public class OperationLogService {
	private static  final Logger logger = LoggerFactory.getLogger(OperationLogService.class);

    public static final OperationLogService me = new OperationLogService();

	private static final String UPLOAD_PATH = PropKit.get("uploadPath") ;

    public void saveOperationLog(Integer UserId, String ip, String modelName, String content) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
        	try {
				OperationLog op = new OperationLog();
				op.setType(Constant.OPERATION_LOG_TYPE_USER).setModelName(modelName).setSysUserId(UserId)
						.setIp(ip).setContent(content).setCreateTime(new Date()).save();
			}catch (Exception ex){
				ex.printStackTrace();
			}
        }).exceptionally(ex -> {
			logger.error("写操作日志异常: " + ex.getMessage());
			return null;
		});

    }

}
