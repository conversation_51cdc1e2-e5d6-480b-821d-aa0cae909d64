package com.sandu.xinye.api.templet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.regex.Matcher;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.RegExpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import com.sandu.xinye.common.model.User;

public class TempletService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(TempletService.class);

    public static final TempletService me = new TempletService();
    private static final String FILE_PATH = "templet";

    public RetKit getTempletPage(int pageNumber, int pageSize, String name, Integer userId, String groupId, String widthRange) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        Kv kvParams = Kv.by("userId", userId).set("groupId", groupId).set("name", name);
        if (StrKit.notBlank(widthRange)) {
            String[] indexRangeArr = widthRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }

        try {
            SqlPara sqlPara = Db.getSqlPara("app.templet.paginate", kvParams);
            Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }

    public RetKit addTemplet(String name, String cover, String gap, Integer height, Integer width,
                             Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId, String blackLabelGap,
                             String blackLabelOffset, Integer cutAfterPrint,  Integer labelType,  boolean autoRename) {
        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }

        name = name.trim();
        if (isExistTemplet(name, userId)) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.TEMPLET_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        return addTemplet(RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)), cover, gap, height, width,
                                printDirection, paperType, machineType, data, userId, blackLabelGap,  blackLabelOffset, cutAfterPrint,labelType,true);
                    } catch (Exception e) {
                        return RetKit.fail("新增失败！");
                    }
                } else if (name.equals(Constant.TEMPLET_DEFAULT_NAME)) {
                    return addTemplet(String.format("%s(1)", name), cover, gap, height, width,
                            printDirection, paperType, machineType, data, userId, blackLabelGap, blackLabelOffset, cutAfterPrint,labelType, true);
                } else {
                    return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
                }
            } else {
                return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
            }
        }

        String newUrl = cover;
        if(!cover.startsWith("http")){  // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        Templet model = new Templet();
        model.setUserId(userId).setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data).setCreateTime(new Date())
                .setBlackLabelGap(blg).setGroupId(-1).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setLabelType(labelType)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW);
        boolean succ = false;
        try{
            succ = model.save();
        }catch (Exception e){
            logger.error(e.getMessage());
        }

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit updateTemplet(String templetId, String name, String cover, String gap, Integer height, Integer width,
                                Integer printDirection, Integer paperType, Integer machineType, String data, Integer userId, String blackLabelGap,
                                String blackLabelOffset, Integer cutAfterPrint,  Integer labelType) {
        if (StrKit.isBlank(templetId)) {
            return RetKit.fail("模板ID是必传的！");
        }
        Templet model = Templet.dao.findById(templetId);
        if (model == null) {
            return RetKit.fail("模板不存在！");
        }
        if(model.getType() == Constant.TEMPLET_TYPE_BUSINESS){
            return RetKit.fail("行业模板不可编辑！");
        }
        Float blg = 0f;
        Float blo = 0f;
        Float bgap = 0f;
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blg = Float.parseFloat(blackLabelGap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标间隔数据是否有误！");
            }
        }
        if (StrKit.notBlank(blackLabelGap)) {
            try {
                blo = Float.parseFloat(blackLabelOffset);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查黑标开端数据是否有误！");
            }
        }
        if (StrKit.notBlank(gap)) {
            try {
                bgap = Float.parseFloat(gap);
            } catch (NumberFormatException e) {
                return RetKit.fail("请检查标签间隙数据是否有误！");
            }
        }
        if (paperType != Templet.PAPER_TYPE_CLEARANCE && paperType != Templet.PAPER_TYPE_CONTINUITY
                && paperType != Templet.PAPER_TYPE_BLACK_LABEL && paperType != Templet.PAPER_TYPE_FIXED_HOLE) {
            return RetKit.fail("纸张类型参数有误！");
        }
        if (!name.equals(model.getName()) && isExistTemplet(name, userId)) {
            return RetKit.fail(RetConstant.CODE_TEMPLET_REPEAT, "模板名称已存在，请修改模板名称！");
        }

        String newUrl = cover;
        if(!cover.startsWith("http")){  // 本地路径，转移到本地保存图片的地址
            newUrl = UploadFileMoveKit.move(cover, FILE_PATH);
        }

        model.setUserId(userId).setName(name).setCover(newUrl).setGap(bgap).setHeight(height).setWidth(width)
                .setPaperType(paperType).setMachineType(machineType).setPrintDirection(printDirection).setData(data)
                .setBlackLabelGap(blg).setGroupId(-1).setBlackLabelOffset(blo)
                .setCutAfterPrint(cutAfterPrint)
                .setLabelType(labelType)
                .setType(Constant.TEMPLET_TYPE_SELF_DEF_NEW)
                .setUpdateTime(new Date());
        boolean succ = model.update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit remove(String id) {
        Templet model = Templet.dao.findById(id);
        if (model == null) {
            return RetKit.fail("找不到该模板");
        }
        boolean succ = Templet.dao.deleteById(id);
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit addGroup(User user, String name, Boolean autoRename) {
        name = name.trim();
        if (isExistGroup(name, user.getUserId())) {
            if (autoRename) {
                Matcher matcher = RegExpKit.matchRegExp(name, RegExpKit.GROUP_RENAME_REGEXP);
                if (matcher != null) {
                    try {
                        Integer number = Integer.valueOf(matcher.group(1));
                        return addGroup(user, RegExpKit.replaceRegExp(name, "(\\d+)", String.valueOf(number + 1)), true);
                    } catch (Exception e) {
                        return RetKit.fail("新增失败");
                    }
                } else {
                    return addGroup(user, String.format("%s(1)", name), true);
                }
            } else {
                return RetKit.fail(RetConstant.CODE_GROUP_REPEAT, "该分组名称已存在！");
            }

        }
        TempletGroup model = new TempletGroup();
        model.setName(name);
        model.setUserId(user.getUserId());
        model.setCreateTime(new Date());
        // 自定义模板分组
        model.setType(Constant.TEMPLET_GROUP_TYPE_SELF_DEF_NEW);

        List<TempletGroup> list = new ArrayList<TempletGroup>();
        boolean succ = model.save();
        if (succ) {
            list = getGroupListByUserId(user.getUserId());
        }
        return succ ? RetKit.ok().set("list", list) : RetKit.fail();
    }

    public RetKit getGroupList(User user) {
        List<TempletGroup> list = getGroupListByUserId(user.getUserId());
        return RetKit.ok("list", list);
    }

    public RetKit moveTempletToGroup(String templetId, String groupId, User user) {
        Templet model = Templet.dao.findById(templetId);
        if (model == null) {
            return RetKit.fail("模板id有误！");
        }
        if (model.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("模板不属于此用户！");
        }
        TempletGroup group = TempletGroup.dao.findById(groupId);
        if (group == null) {
            return RetKit.fail("分组id有误！");
        }
        if (group.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("分组不属于此用户！");
        }
        boolean succ = model.setGroupId(Integer.valueOf(groupId)).update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit updateTempletGroupName(String id, String name) {
        TempletGroup target = TempletGroup.dao.findById(id);
        if (target == null) {
            return RetKit.fail("参数有误！");
        }
        if (isExistGroupById(name.trim(), target.getUserId(), target.getId())) {
            return RetKit.fail(RetConstant.CODE_GROUP_REPEAT, "该分组名称已存在！");
        }
        boolean succ = target.setName(name.trim()).update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit deleteTempletGroup(String groupId, User user) {
        TempletGroup model = TempletGroup.dao.findById(groupId);
        if (model == null) {
            return RetKit.fail("参数有误！");
        }
        if (model.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("当前分组不属于此用户！");
        }
        boolean succ = Db.tx(new IAtom() {

            @Override
            public boolean run() throws SQLException {
                boolean succ = model.delete();
                boolean succ1 = Db.update("delete from templet where groupId=?", groupId) >= 0;
                return succ && succ1;
            }

        });
        List<TempletGroup> list = new ArrayList<TempletGroup>();
        if (succ) {
            list = getGroupListByUserId(user.getUserId());
        }
        return succ ? RetKit.ok().set("list", list) : RetKit.fail();
    }

    public RetKit getTemplateShareSession(long templateId, User user) {
        Templet model = Templet.dao.findById(templateId);
        if (model == null) {
            return RetKit.fail("参数有误！");
        }
        if (model.getType() != Constant.TEMPLET_TYPE_BUSINESS && model.getUserId().intValue() != user.getUserId().intValue()) {
            return RetKit.fail("当前模板不属于此用户！");
        }

        String session = getRandomCode(20);
        CacheKit.put(CacheConstant.TEMPLET_SHARE, session, templateId);
        logger.info("获取分享口令, session:" + session);

        return RetKit.ok().set("data", session);
    }

    public RetKit getTemplateByShareSession(String session, User user) {
        Object templateId = CacheKit.get(CacheConstant.TEMPLET_SHARE, session);
        if (templateId == null) {
            return RetKit.fail("session不存在或已失效！");
        }

        Templet model = Templet.dao.findById(templateId);
        if (model == null) {
            return RetKit.fail("模板不存在！");
        }
        if (model.getUserId().intValue() == user.getUserId().intValue()) {
            return RetKit.fail("模板不能分享给自己！");
        }

        return RetKit.ok().set("data", model);
    }

    private boolean isExistTemplet(String name, int userId) {
        Templet exist = Templet.dao.findFirst("select * from templet where name=? and userId=?", name, userId);
        return exist != null ? true : false;
    }

    private boolean isExistGroup(String name, int userId) {
        TempletGroup isExist = TempletGroup.dao.findFirst("select id from templet_group where name=? and userId=?",
                name, userId);
        return isExist != null;
    }

    private boolean isExistGroupById(String name, int userId, int id) {
        TempletGroup isExist = TempletGroup.dao
                .findFirst("select id from templet_group where name=? and userId=? and id != ?", name, userId, id);
        return isExist != null;
    }

    private List<TempletGroup> getGroupListByUserId(int userId) {
        List<TempletGroup> list = TempletGroup.dao.find("select id as groupId,name from templet_group where userId=? order by id desc",
                userId);
        return list;
    }

    private String getRandomCode(int length) {
        String Val = "";
        Random rand = new Random();
        for (int i = 0; i < length; i++) {
            String charOrNum = rand.nextInt(3) % 3 == 0 ? "char" : "num";  //用这个来随机产生字母还是数字, 1:2的比例
            if ("char".equalsIgnoreCase(charOrNum)) {
                //然后字母有大小写问题
                int choice = 97;    //加上97就是小写
                Val += (char) (choice + rand.nextInt(26));
            }
            if ("num".equalsIgnoreCase(charOrNum)) {
                Val += String.valueOf(rand.nextInt(10));
            }
        }
        return Val;
    }

}
