package com.sandu.xinye.admin.set;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;

public class DownloadSetController extends AdminController {

    private static final DownloadSetService srv = DownloadSetService.me;

    @Clear
    public void index() {
        render("/admin_index.html");
    }

    public void getSet() {
        RetKit ret = srv.getSet();
        System.out.println(ret);
        renderJson(ret);
    }

    public void update() {
        String id = getPara("id");
        String url = getPara("url");
        RetKit ret = srv.update(id, url);
        renderJson(ret);
    }
}
