package com.sandu.xinye.admin.dataBase;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.TempletGroup;
import com.sandu.xinye.common.model.User;

import java.util.List;

public class UserService {

	public static final UserService me = new UserService();

	public RetKit list(int pageNumber, int pageSize, Kv kv) {
		String userPhone = kv.getStr("userPhone");
		kv.set("userPhone", userPhone);
		SqlPara sqlPara = Db.getSqlPara("admin.user.paginate", kv);
		Page<User> page = User.dao.paginate(pageNumber, pageSize, sqlPara);
		return RetKit.ok("page", page);
	}

	public RetKit getUserGroupPage(int pageNumber, int pageSize, String userId) {
		Page<TempletGroup> page = TempletGroup.dao.paginate(pageNumber, pageSize, "select * ",
				" from templet_group where userId=?", userId);
		return RetKit.ok("page", page);
	}

	public RetKit getAppUserList(String keyword) {
		if (StrKit.isBlank(keyword)) {
			return RetKit.fail();
		}

		List<User> list = getUserListByKeyword(keyword);
		return RetKit.ok("list",list);
	}

	private List<User> getUserListByKeyword(String keyword) {
		List<User> list = User.dao.find("select userId, userNickName, userPhone from user where userNickName = ? or userPhone=? order by userId desc",
				keyword,keyword);
		return list;
	}

}
