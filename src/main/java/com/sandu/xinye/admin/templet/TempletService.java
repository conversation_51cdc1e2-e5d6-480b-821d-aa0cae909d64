package com.sandu.xinye.admin.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.AESKit;
import com.sandu.xinye.common.kit.AliOssKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUser;
import com.sandu.xinye.common.model.Templet;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class TempletService {
	public static final TempletService me = new TempletService();

	private static final String ossImgHost = "http://public.ycjqb.com/";
	private static final String ossBucketName = "buk-xprinter";
	private static final String userTempletOssBucketName = "xprinter-private";
	private static final String userTempletOssHost = "http://img.ycjqb.com/";
	private static final String objectPrefix = "templet/usermake/";

	public RetKit list(int pageSize, int pageNumber, Kv kv) {
		String groupId = kv.getStr("groupId");
		String templetName = kv.getStr("templateName");
		if (StrKit.notBlank(groupId)) {
			kv.set("groupId", groupId);
		}
		if (StrKit.notBlank(templetName)) {
			templetName = "%" + templetName + "%";
			kv.set("name", templetName);
		}
		SqlPara sqlPara = Db.getSqlPara("admin.templet.paginate", kv);
		Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);

		// 循环解密cover
		String coverFileName = "";
		for (Templet tpl : page.getList()) {
			String cover = tpl.getCover();
			int coverFileNameIndex = cover.lastIndexOf("/");
			if (coverFileNameIndex == -1) {
				LogKit.info("coverFileNameIndex == -1, cover" + cover);
			}
			coverFileName = cover.substring(coverFileNameIndex + 1);
			coverFileName = AESKit.decrypt(coverFileName);
			if (coverFileNameIndex == -1) {
				tpl.setCover(cover);
			} else {
				tpl.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);
			}

		}

		return RetKit.ok("page", page);
	}

	public RetKit add(Templet templet, SysUser sysUser, String ip) {
		String cover = templet.getCover();
		int coverFileNameIndex = cover.lastIndexOf("/");
		String coverFileName = cover.substring(coverFileNameIndex + 1);
		try {
			coverFileName = AESKit.encrypt(coverFileName);
		} catch (Exception e) {
			LogKit.error("添加模板失败：" + e.getMessage());
			return RetKit.fail();
		}

		String templetData = templet.getData();
		if (StrKit.isBlank(templetData)) {
			templetData = String.format("[{\"elementType\":3}]\"");
		}
		templet.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);

		boolean succ = templet.setGap(2f).setPaperType(Constant.PAPER_TYPE_1).setPrintDirection(Constant.PRINT_DIRECTION_0)
				.setData(templetData).setBlackLabelGap(2f).setBlackLabelOffset(2f)
				.setType(Constant.TEMPLET_TYPE_BUSINESS).setCreateTime(new Date()).setUserId(sysUser.getSysUserId()).save();
		if (succ) {
			String content = sysUser.getSysUserName() + "添加了id为" + templet.getId() + "的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit update(Templet templet, SysUser sysUser, String ip) {
		String cover = templet.getCover();
		int coverFileNameIndex = cover.lastIndexOf("/");
		String coverFileName = cover.substring(coverFileNameIndex + 1);
		try {
			coverFileName = AESKit.encrypt(coverFileName);
		} catch (Exception e) {
			LogKit.error("添加模板失败：" + e.getMessage());
			return RetKit.fail();
		}

		templet.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);

		boolean succ = templet.setUserId(sysUser.getSysUserId()).update();
		if (succ) {
			String content = sysUser.getSysUserName() + "编辑了id为" + templet.getId() + "的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("编辑成功") : RetKit.fail();
	}

	public RetKit del(String id, SysUser sysUser, String ip) {
		Templet templet = Templet.dao.findById(id);
		boolean succ = templet.delete();
		if (succ) {
			String content = sysUser.getSysUserName() + "删除了id为" + id + "的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("删除成功") : RetKit.fail();
	}

	private String getFileNameOfCover(String cover) {
		int coverFileNameIndex = cover.lastIndexOf("/");
		return cover.substring(coverFileNameIndex + 1);

	}
  public RetKit isExistEancode(String eancode) {
    Templet exist = Templet.dao.findFirst("select * from templet where type = 1 and eancode=? ", eancode);
    boolean isExist = exist != null ? true : false;
    return RetKit.ok("isExist", isExist);
  }

	public RetKit getTempletList(String userId, String groupId, String type) {
		if (StrKit.isBlank(groupId)) {
			return RetKit.fail("groupId不能为空");
		}
		if (type != null && !type.equals("1") && StrKit.isBlank(userId)) {
			return RetKit.fail("userId不能为空");
		}
		String sql = String.format("select * from templet where groupId = %s ", groupId);
		if (StrKit.notBlank(userId)) {
			sql += " and userId = " + userId;
		}
		if (StrKit.notBlank(type)) {
			sql += " and type = " + type;
		} else {
			sql += " and type != 1";
		}
		sql += " order by id desc";
		List<Templet> list = Templet.dao.find(sql);
		return RetKit.ok("list", list);
	}

	public RetKit batchUploadToCloud(String serverPath, String[] ids, int groupId, SysUser sysUser, String ip) {
		if (ids.length == 0 || groupId == -1) {
			return RetKit.fail("参数不正确！");
		}

		//记录开始时间
		Long start = System.currentTimeMillis();
		List<CompletableFuture<String>> fList = new ArrayList<>();
		List<String> errorMsg = new ArrayList<>();
		Arrays.stream(ids).forEach(id -> {
			CompletableFuture<String> f = CompletableFuture.supplyAsync(
					//执行耗时较长的业务代码
					() -> _uploadToCloud(serverPath, id, groupId, sysUser, ip)
			).handle(new BiFunction<String, Throwable, String>() {
				@Override
				public String apply(String s, Throwable throwable) {
					if(StrUtil.isNotEmpty(s)){
						errorMsg.add(s);
					}
					return s;
				}
			});
			fList.add(f);
		});
		// 阻塞，等待所有任务执行完成
		CompletableFuture[] completableFutures = fList.toArray(new CompletableFuture[fList.size()]);
		try {
			CompletableFuture.allOf(completableFutures).get();
			LogKit.info("所有任务执行完成触发\n errorMsg=" + errorMsg.stream().collect(Collectors.joining(",")) + "\n耗时=" + (System.currentTimeMillis() - start));

			if (CollectionUtil.isNotEmpty(errorMsg)) {
				return RetKit.fail(String.format("%s", String.join(System.lineSeparator(), errorMsg)));
			} else {
				return RetKit.ok("批量上传成功!");
			}
		} catch (Exception ex) {
			LogKit.error("上传到行业模板报错：" + ex.getMessage());
		}

		return RetKit.fail("批量上传行业模板失败！");

	}

	/**
	 * 上传用户模板到行业模板
	 *
	 * @param templetId          待上传模板ID（用户模板）
	 * @param templetBusiGroupId 上传到行业模板分组ID
	 * @return 返回错误信息
	 */
	private String _uploadToCloud(String serverPath, String templetId, int templetBusiGroupId, SysUser sysUser, String ip) {
		Templet model = Templet.dao.findById(templetId);
		// 修改模板名称，增加 宽高展示
		String templetName = String.format("%s（%s*%s）", model.getName(), model.getWidth(), model.getHeight());
		model.setName(templetName);
		// cover需要上传到OSS
		String originCover = model.getCover();

		if(isExistBusiTemplet(templetName, templetBusiGroupId)){
			return String.format("模板【%s】已存在，上传失败！", templetName);
		}

		// cover文件名称需要加密
		String coverFileName = "";
		String extName = originCover.substring(originCover.lastIndexOf('.'));
		try{
			templetName = URLEncoder.encode(templetName, "UTF-8");
		}catch (Exception ex){

		}
		String ossName = templetName + extName;
		String coverPath = originCover.startsWith("http") ? originCover : serverPath + originCover;
		if (uploadCoverToOss(coverPath, ossName)) {
			try {
				coverFileName = AESKit.encrypt(ossName);
			} catch (Exception e) {
				LogKit.error("模板加密失败：" + e.getMessage());
				return String.format("模板【%s】封面加密失败，上传失败！", templetName);
			}
			model.setCover(ossImgHost + objectPrefix + coverFileName);

			LogKit.info("上传到行业模板... 模板名称：" + templetName);
			try {
				boolean succ = model.setId(0)
						.setGroupId(templetBusiGroupId).setType(Constant.TEMPLET_GROUP_TYPE_BUSINESS)
						.setCreateTime(new Date()).setUpdateTime(new Date())
						.save();
				if (succ) {
					LogKit.info("上传到行业模板成功...");
					String content = model.getUserId() + "添加了id为" + model.getId() + "的模板【" + model.getName() + "】";
					OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
					return "";
				}
			} catch (Exception e) {
				LogKit.error(e.getMessage());
				return String.format("模板【%s】保存失败！", templetName);
			}
		}else {
			return String.format("模板【%s】封面上传阿里云失败！", templetName);
		}

		return "";
	}

	private boolean isExistBusiTemplet(String name, int groupId) {
		Templet exist = Templet.dao.findFirst("select * from templet where type = 1 and name=? and groupId=?", name, groupId);
		return exist != null ? true : false;
	}

	public RetKit uploadToCloud(String serverPath, int templetId, int groupId, SysUser sysUser, String ip) {
		if (templetId == -1 || groupId == -1) {
			return RetKit.fail("参数不正确！");
		}
		Templet model = Templet.dao.findById(templetId);
		// 修改模板名称，增加 宽高展示
		String templetName = String.format("%s（%s*%s）", model.getName(), model.getWidth(), model.getHeight());
		model.setName(templetName);
		// cover需要上传到OSS
		String originCover = model.getCover();
		// cover文件名称需要加密
		String coverFileName = "";
		String extName = originCover.substring(originCover.lastIndexOf('.'));
		try{
			templetName = URLEncoder.encode(templetName, "UTF-8");
		}catch (Exception ex){

		}
		String ossName = templetName + extName;
		String coverPath = originCover.startsWith("http") ? originCover : serverPath + originCover;
		if (uploadCoverToOss(coverPath, ossName)) {
			try {
				coverFileName = AESKit.encrypt(ossName);
			} catch (Exception e) {
				LogKit.error("模板加密失败：" + e.getMessage());
				return RetKit.fail("上传失败！");
			}
			model.setCover(ossImgHost + objectPrefix + coverFileName);

			boolean succ = model.setId(0)
					.setGroupId(groupId).setType(Constant.TEMPLET_GROUP_TYPE_BUSINESS)
					.setCreateTime(new Date()).setUpdateTime(new Date())
					.save();
			if (succ) {
				String content = model.getUserId() + "添加了id为" + model.getId() + "的模板【" + model.getName() + "】";
				OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
			}
			return succ ? RetKit.ok("上传成功!") : RetKit.fail("上传失败!");
		} else {
			return RetKit.fail("OSS上传失败！");
		}


	}


	private boolean uploadCoverToOss(String filePath, String ossName) {
		boolean result;

		try {
			//oss路径
			if (filePath.startsWith(userTempletOssHost)) {
				String sourceKey = filePath.replace(userTempletOssHost, "");
				String objectName = objectPrefix + URLDecoder.decode(ossName, "UTF-8");

				result = AliOssKit.copyObject(userTempletOssBucketName, sourceKey, ossBucketName, objectName);
			} else {
				File file = getFile(filePath);
				String objectName = objectPrefix + ossName;

				result = AliOssKit.createObject(ossBucketName, objectName, file);
			}
		} catch (Exception e) {
			LogKit.error(e.getMessage());
			result = false;
		}

		return result;
	}


	public static File getFile(String url) throws Exception {
		//对本地文件命名
		String fileName = url.substring(url.lastIndexOf("."), url.length());
		File file = null;

		URL urlfile;
		InputStream inStream = null;
		OutputStream os = null;
		try {
			file = File.createTempFile("net_url", fileName);
			//下载
			urlfile = new URL(url);
			inStream = urlfile.openStream();
			os = new FileOutputStream(file);

			int bytesRead = 0;
			byte[] buffer = new byte[8192];
			while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (null != os) {
					os.close();
				}
				if (null != inStream) {
					inStream.close();
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		return file;
	}
}
