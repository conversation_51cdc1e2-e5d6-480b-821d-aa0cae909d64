package com.sandu.xinye.admin.templet;

import com.jfinal.core.Controller;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.TextInApiClient;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import com.alibaba.fastjson.JSON;
import java.io.File;

/**
 * OCR调试控制器
 * 用于获取TextIn API的原始响应数据进行调试
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class DebugOcrController extends Controller {
    
    /**
     * 获取TextIn API原始响应
     * 访问: /admin/debug/textin
     */
    public void textin() {
        try {
            UploadFile uploadFile = getFile("file");
            if (uploadFile == null) {
                renderJson(RetKit.fail("请上传图片文件"));
                return;
            }
            
            File imageFile = uploadFile.getFile();
            
            // 直接调用TextIn API
            TextInApiClient apiClient = TextInApiClient.me;
            TextInResponse response = apiClient.recognizeImage(imageFile);
            
            // 返回完整的JSON响应
            String jsonResponse = JSON.toJSONString(response, true);
            
            // 同时在控制台输出（方便查看）
            System.out.println("==========================================");
            System.out.println("🔍 TextIn API 原始响应 (调试接口):");
            System.out.println("==========================================");
            System.out.println(jsonResponse);
            System.out.println("==========================================");
            
            renderJson(RetKit.ok("获取成功", jsonResponse));
            
        } catch (Exception e) {
            e.printStackTrace();
            renderJson(RetKit.fail("调试失败: " + e.getMessage()));
        }
    }
    
    /**
     * 分析图像元素
     * 访问: /admin/debug/analyze
     */
    public void analyze() {
        try {
            UploadFile uploadFile = getFile("file");
            if (uploadFile == null) {
                renderJson(RetKit.fail("请上传图片文件"));
                return;
            }
            
            File imageFile = uploadFile.getFile();
            
            // 调用TextIn API
            TextInApiClient apiClient = TextInApiClient.me;
            TextInResponse response = apiClient.recognizeImage(imageFile);
            
            // 分析响应
            StringBuilder analysis = new StringBuilder();
            analysis.append("📊 TextIn响应分析报告:\n");
            analysis.append("==========================================\n");
            
            if (response != null && response.getResult() != null) {
                java.util.Map<String, Object> result = response.getResult();
                
                // 分析pages
                @SuppressWarnings("unchecked")
                java.util.List<java.util.Map<String, Object>> pages = 
                    (java.util.List<java.util.Map<String, Object>>) result.get("pages");
                
                if (pages != null && !pages.isEmpty()) {
                    java.util.Map<String, Object> firstPage = pages.get(0);
                    
                    // 分析content
                    @SuppressWarnings("unchecked")
                    java.util.List<java.util.Map<String, Object>> content = 
                        (java.util.List<java.util.Map<String, Object>>) firstPage.get("content");
                    
                    if (content != null) {
                        analysis.append("📋 Content数组: ").append(content.size()).append("个元素\n");
                        
                        int imageCount = 0;
                        java.util.Map<String, Integer> subTypeCount = new java.util.HashMap<>();
                        
                        for (java.util.Map<String, Object> item : content) {
                            String type = (String) item.get("type");
                            if ("image".equals(type)) {
                                imageCount++;
                                String subType = (String) item.get("sub_type");
                                subTypeCount.put(subType != null ? subType : "null",
                                               subTypeCount.getOrDefault(subType, 0) + 1);
                                
                                analysis.append("🖼️ 发现图像元素: sub_type=").append(subType).append("\n");
                                
                                // 检查data字段
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> data = 
                                    (java.util.Map<String, Object>) item.get("data");
                                if (data != null && data.containsKey("image_url")) {
                                    analysis.append("   🔗 image_url: ").append(data.get("image_url")).append("\n");
                                }
                            }
                        }
                        
                        analysis.append("\n📊 统计结果:\n");
                        analysis.append("图像元素总数: ").append(imageCount).append("\n");
                        analysis.append("sub_type分布: ").append(subTypeCount).append("\n");
                        
                        // 预测elementType=8的数量
                        int willBecomeImageElements = 0;
                        for (java.util.Map.Entry<String, Integer> entry : subTypeCount.entrySet()) {
                            String subType = entry.getKey();
                            int count = entry.getValue();
                            if (!"qrcode".equals(subType) && !"barcode".equals(subType)) {
                                willBecomeImageElements += count;
                            }
                        }
                        
                        analysis.append("🎯 预计生成elementType=8的数量: ").append(willBecomeImageElements).append("\n");
                        
                        if (willBecomeImageElements == 0) {
                            analysis.append("⚠️ 解释: 所有图像都是qrcode/barcode类型，不会产生图片元素\n");
                        }
                        
                    } else {
                        analysis.append("❌ content数组为空\n");
                    }
                } else {
                    analysis.append("❌ pages数组为空\n");
                }
            } else {
                analysis.append("❌ TextIn响应无效\n");
            }
            
            analysis.append("==========================================\n");
            
            // 输出到控制台
            System.out.println(analysis.toString());
            
            renderJson(RetKit.ok("分析完成", analysis.toString()));
            
        } catch (Exception e) {
            e.printStackTrace();
            renderJson(RetKit.fail("分析失败: " + e.getMessage()));
        }
    }
}