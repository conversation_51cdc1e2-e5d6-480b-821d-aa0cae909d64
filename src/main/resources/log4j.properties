log4j.rootLogger=INFO, stdout, file
# 设置 com.vida.xinye 包下的日志级别为 DEBUG
log4j.logger.com.sandu.xinye=INFO
###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.Encoding=UTF-8
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
# Output to the File
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.Threshold=INFO
log4j.appender.file.File=./logs/xinye.log
log4j.appender.file.Encoding=UTF-8
log4j.appender.file.Append=true 
# 文件超出10MB就创建新的日志文件
log4j.appender.file.MaxFileSize=10240KB 
log4j.appender.file.MaxBackupIndex=60
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
#功能耗时、异常捕获日志
#log4j.logger.timeAround=info,timeAround
#log4j.appender.timeAround=org.apache.log4j.RollingFileAppender
#log4j.appender.timeAround.File=./logs/timeAround.log
#log4j.appender.timeAround.MaxFileSize=200MB
#log4j.appender.timeAround.MaxBackupIndex=10
#log4j.appender.timeAround.layout=org.apache.log4j.PatternLayout
#log4j.appender.timeAround.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
#log4j.additivity.timeAround=false
