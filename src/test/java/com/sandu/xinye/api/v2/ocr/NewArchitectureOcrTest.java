package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 新架构OCR测试
 * 验证基于detail数组的新架构是否正常工作
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class NewArchitectureOcrTest {
    
    private OcrService ocrService;
    private TextInApiClient textInApiClient;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        ocrService = OcrService.me;
        textInApiClient = TextInApiClient.me;
    }
    
    @Test
    public void testNewArchitectureWithShoeTable() {
        try {
            System.out.println("🔧 测试新架构 - 鞋子表格图片");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\鞋子（60_40）-2.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 1. 直接调用TextIn API获取原始数据
            System.out.println("📥 调用TextIn API...");
            TextInResponse rawResponse = textInApiClient.recognizeImage(imageFile);
            
            if (rawResponse == null) {
                System.out.println("❌ TextIn API调用失败");
                return;
            }
            
            // 2. 分析原始数据结构
            analyzeRawResponseStructure(rawResponse);
            
            // 3. 使用新架构进行转换
            System.out.println("\n🔄 使用新架构转换...");
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "鞋子（60_40）-2.png");
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (result.isTrue("success")) {
                OcrResponse ocrResponse = (OcrResponse) result.get("data");
                analyzeNewArchitectureResult(ocrResponse);
            } else {
                System.out.println("❌ 新架构OCR服务调用失败: " + result.getStr("msg"));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    @Test
    public void testNewArchitectureWithTeaShopQR() {
        try {
            System.out.println("🔧 测试新架构 - 茶庄二维码+条形码图片");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\xxx茶庄.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 调用OCR服务使用新架构
            System.out.println("🔄 使用新架构进行OCR...");
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "xxx茶庄.png");
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (result.isTrue("success")) {
                OcrResponse ocrResponse = (OcrResponse) result.get("data");
                analyzeNewArchitectureResult(ocrResponse);
                
                // 特别验证二维码和条形码识别
                validateQRAndBarcodeRecognition(ocrResponse);
            } else {
                System.out.println("❌ 新架构OCR服务调用失败: " + result.getStr("msg"));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 分析TextIn原始响应的数据结构
     */
    @SuppressWarnings("unchecked")
    private void analyzeRawResponseStructure(TextInResponse rawResponse) {
        System.out.println("\n📊 原始数据结构分析:");
        
        // 分析detail数组
        List<Map<String, Object>> detailList = rawResponse.getDetail();
        System.out.println("Detail数组: " + (detailList != null ? detailList.size() + "个元素" : "null"));
        
        if (detailList != null) {
            for (int i = 0; i < Math.min(detailList.size(), 5); i++) {
                Map<String, Object> detail = detailList.get(i);
                System.out.println("  detail[" + i + "]: type=" + detail.get("type") + 
                                 ", sub_type=" + detail.get("sub_type") + 
                                 ", content_index=" + detail.get("content"));
            }
        }
        
        // 分析pages数组
        List<Map<String, Object>> pages = rawResponse.getPages();
        if (pages != null && !pages.isEmpty()) {
            Map<String, Object> firstPage = pages.get(0);
            List<Map<String, Object>> content = (List<Map<String, Object>>) firstPage.get("content");
            List<Map<String, Object>> structured = (List<Map<String, Object>>) firstPage.get("structured");
            
            System.out.println("Pages[0] content数组: " + (content != null ? content.size() + "个元素" : "null"));
            System.out.println("Pages[0] structured数组: " + (structured != null ? structured.size() + "个元素" : "null"));
        }
    }
    
    /**
     * 分析新架构转换结果
     */
    private void analyzeNewArchitectureResult(OcrResponse ocrResponse) {
        System.out.println("\n📄 新架构转换结果分析:");
        List<Map<String, Object>> elements = ocrResponse.getElements();
        System.out.println("元素总数: " + elements.size());
        
        // 统计各种元素类型
        int textCount = 0, tableCount = 0, qrCount = 0, barcodeCount = 0;
        
        for (Map<String, Object> element : elements) {
            String elementType = String.valueOf(element.get("elementType"));
            
            switch (elementType) {
                case "1": // 文本
                    textCount++;
                    break;
                case "2": // 条形码
                    barcodeCount++;
                    break;
                case "7": // 二维码
                    qrCount++;
                    break;
                case "10": // 表格
                    tableCount++;
                    break;
            }
        }
        
        System.out.println("📊 元素统计:");
        System.out.println("  文本元素: " + textCount + "个");
        System.out.println("  条形码元素: " + barcodeCount + "个");
        System.out.println("  二维码元素: " + qrCount + "个");
        System.out.println("  表格元素: " + tableCount + "个");
        
        // 显示详细信息
        System.out.println("\n📋 元素详情:");
        for (int i = 0; i < Math.min(elements.size(), 10); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));
            String content = String.valueOf(element.get("content"));
            
            if (content.length() > 50) {
                content = content.substring(0, 47) + "...";
            }
            
            System.out.println("  [" + i + "] type=" + elementType + ", content='" + content + "'");
        }
    }
    
    /**
     * 验证二维码和条形码识别效果
     */
    private void validateQRAndBarcodeRecognition(OcrResponse ocrResponse) {
        System.out.println("\n🔍 验证二维码和条形码识别:");
        
        boolean foundQR = false, foundBarcode = false;
        
        for (Map<String, Object> element : ocrResponse.getElements()) {
            String elementType = String.valueOf(element.get("elementType"));
            String content = String.valueOf(element.get("content"));
            
            if ("7".equals(elementType)) {
                foundQR = true;
                System.out.println("✅ 发现二维码: " + content);
            } else if ("2".equals(elementType)) {
                foundBarcode = true;
                System.out.println("✅ 发现条形码: " + content);
            }
        }
        
        if (!foundQR) {
            System.out.println("❌ 未识别到二维码");
        }
        if (!foundBarcode) {
            System.out.println("❌ 未识别到条形码");
        }
        
        System.out.println("🏁 识别结果: QR=" + (foundQR ? "成功" : "失败") + 
                         ", Barcode=" + (foundBarcode ? "成功" : "失败"));
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("new_arch_test_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}