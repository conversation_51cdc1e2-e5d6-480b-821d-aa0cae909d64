package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import static org.junit.Assert.*;

import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import java.io.File;
import java.util.Map;
import java.util.List;

/**
 * 图片元素修复验证测试
 * 验证所有type=image元素都能正确转换为图片元素
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class ImageElementFixTest {
    
    @Test
    public void testImageElementFixWithDuoduoImage() {
        try {
            System.out.println("🎯 测试图片元素修复 - 使用多多买菜图片");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\多多买菜（40_30）.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("❌ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 创建UploadFile
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "多多买菜（40_30）.png");
            
            // 调用OCR服务
            OcrService ocrService = OcrService.me;
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (result.isTrue("success")) {
                System.out.println("✅ OCR识别成功");
                
                OcrResponse response = (OcrResponse) result.get("data");
                List<Map<String, Object>> elements = response.getElements();
                
                System.out.println("📊 识别结果统计:");
                System.out.println("总元素数量: " + elements.size());
                
                // 统计各类型元素
                int textCount = 0, barcodeCount = 0, qrcodeCount = 0, imageCount = 0, tableCount = 0;
                
                for (Map<String, Object> element : elements) {
                    String elementType = (String) element.get("elementType");
                    
                    switch (elementType) {
                        case "1": textCount++; break;
                        case "2": barcodeCount++; break;
                        case "7": qrcodeCount++; break;
                        case "8": imageCount++; break;  // 重点关注
                        case "10": tableCount++; break;
                    }
                }
                
                System.out.println("文本元素(1): " + textCount);
                System.out.println("条形码(2): " + barcodeCount);
                System.out.println("二维码(7): " + qrcodeCount);
                System.out.println("🎯 图片元素(8): " + imageCount + " ⭐");
                System.out.println("表格(10): " + tableCount);
                
                // 详细展示图片元素
                if (imageCount > 0) {
                    System.out.println("\n📸 图片元素详情:");
                    for (Map<String, Object> element : elements) {
                        if ("8".equals(element.get("elementType"))) {
                            System.out.println("  - imageType: " + element.get("imageType"));
                            System.out.println("    imageUrl: " + element.get("imageUrl"));
                            System.out.println("    confidence: " + element.get("confidence"));
                            System.out.println("    位置: (" + element.get("x") + "," + element.get("y") + 
                                             ") 尺寸: " + element.get("width") + "x" + element.get("height"));
                            System.out.println();
                        }
                    }
                    
                    System.out.println("🎉 修复成功！识别到了 " + imageCount + " 个图片元素");
                } else {
                    System.out.println("⚠️ 仍未识别到图片元素");
                    System.out.println("可能原因：");
                    System.out.println("1. TextIn没有返回任何type=image数据");
                    System.out.println("2. 所有type=image都是qrcode/barcode类型");
                    System.out.println("3. imageUrl提取失败");
                }
                
            } else {
                System.out.println("❌ OCR识别失败: " + result.getMsg());
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends com.jfinal.upload.UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("image_fix_test_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}