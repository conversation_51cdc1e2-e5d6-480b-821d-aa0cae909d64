package com.sandu.xinye.api.v2.login;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.Kv;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import org.junit.Before;
import org.junit.Test;

/**
 * 用户登录服务测试
 * 验证登录接口是否正确返回推广码信息
 * 
 * <AUTHOR> Team
 * @since 2025-08-06
 */
public class UserLoginServiceTest {
    
    @Before
    public void setUp() {
        // 确保配置文件已加载
        try {
            PropKit.use("common_config.txt");
        } catch (Exception e) {
            System.out.println("配置文件未找到，使用默认配置进行测试");
        }
    }
    
    @Test
    public void testLoginResponseFormat() {
        System.out.println("=== 测试登录返回数据格式 ===");
        
        // 创建一个模拟用户对象
        User mockUser = new User();
        mockUser.setUserId(12345)
               .setUserNickName("测试用户")
               .setUserPhone("13800138000")
               .setIdentity("ABC123")
               .setUserImg("/upload/avatar/test.jpg")
               .setPromotionCode("DU2UBA")
               .setPromotionBindTime(new java.util.Date());
        
        // 模拟登录服务的addPromotionCodeInfo方法逻辑
        Kv kv = Kv.by("userId", mockUser.getUserId())
                  .set("userNickName", mockUser.getUserNickName())
                  .set("userPhone", mockUser.getUserPhone())
                  .set("shareCode", mockUser.getIdentity())
                  .set("userImg", mockUser.getUserImg())
                  .set("accessToken", "mock_access_token")
                  .set("vipInfo", mockUser.buildVipInfo());
        
        // 添加推广码信息（模拟addPromotionCodeInfo方法）
        String promotionCode = mockUser.getPromotionCode();
        boolean hasPromotionCode = promotionCode != null && !promotionCode.trim().isEmpty();
        
        kv.set("promotionCode", hasPromotionCode ? promotionCode : null)
          .set("promotionBindTime", hasPromotionCode ? mockUser.getPromotionBindTime() : null)
          .set("hasPromotionCode", hasPromotionCode);
        
        // 输出测试结果
        System.out.println("登录返回数据：");
        System.out.println("- userId: " + kv.get("userId"));
        System.out.println("- userNickName: " + kv.get("userNickName"));
        System.out.println("- userPhone: " + kv.get("userPhone"));
        System.out.println("- shareCode: " + kv.get("shareCode"));
        System.out.println("- userImg: " + kv.get("userImg"));
        System.out.println("- accessToken: " + kv.get("accessToken"));
        System.out.println("- hasPromotionCode: " + kv.get("hasPromotionCode"));
        System.out.println("- promotionCode: " + kv.get("promotionCode"));
        System.out.println("- promotionBindTime: " + kv.get("promotionBindTime"));
        
        // 验证推广码字段
        assert kv.get("hasPromotionCode").equals(true);
        assert kv.get("promotionCode").equals("DU2UBA");
        assert kv.get("promotionBindTime") != null;
        
        System.out.println("✅ 推广码信息返回正确！");
    }
    
    @Test
    public void testLoginResponseFormatWithoutPromotionCode() {
        System.out.println("\n=== 测试无推广码用户登录返回数据格式 ===");
        
        // 创建一个没有推广码的模拟用户对象
        User mockUser = new User();
        mockUser.setUserId(54321)
               .setUserNickName("无推广码用户")
               .setUserPhone("13900139000")
               .setIdentity("XYZ789")
               .setUserImg("/upload/avatar/test2.jpg");
        // 没有设置推广码
        
        // 模拟登录服务的addPromotionCodeInfo方法逻辑
        Kv kv = Kv.by("userId", mockUser.getUserId())
                  .set("userNickName", mockUser.getUserNickName())
                  .set("userPhone", mockUser.getUserPhone())
                  .set("shareCode", mockUser.getIdentity())
                  .set("userImg", mockUser.getUserImg())
                  .set("accessToken", "mock_access_token_2")
                  .set("vipInfo", mockUser.buildVipInfo());
        
        // 添加推广码信息（模拟addPromotionCodeInfo方法）
        String promotionCode = mockUser.getPromotionCode();
        boolean hasPromotionCode = promotionCode != null && !promotionCode.trim().isEmpty();
        
        kv.set("promotionCode", hasPromotionCode ? promotionCode : null)
          .set("promotionBindTime", hasPromotionCode ? mockUser.getPromotionBindTime() : null)
          .set("hasPromotionCode", hasPromotionCode);
        
        // 输出测试结果
        System.out.println("登录返回数据：");
        System.out.println("- userId: " + kv.get("userId"));
        System.out.println("- userNickName: " + kv.get("userNickName"));
        System.out.println("- hasPromotionCode: " + kv.get("hasPromotionCode"));
        System.out.println("- promotionCode: " + kv.get("promotionCode"));
        System.out.println("- promotionBindTime: " + kv.get("promotionBindTime"));
        
        // 验证推广码字段
        assert kv.get("hasPromotionCode").equals(false);
        assert kv.get("promotionCode") == null;
        assert kv.get("promotionBindTime") == null;
        
        System.out.println("✅ 无推广码用户信息返回正确！");
    }
}