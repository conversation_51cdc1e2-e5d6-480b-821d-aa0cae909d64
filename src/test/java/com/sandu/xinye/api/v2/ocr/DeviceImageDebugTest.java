package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 设备标识3图片专项调试测试
 * 分析TextIn API对该图片的原始返回数据
 * 
 * <AUTHOR>  
 * @date 2025-08-08
 */
public class DeviceImageDebugTest {
    
    @Test
    public void debugDeviceImage3Recognition() {
        System.out.println("🔍 设备标识3图片识别调试");
        System.out.println("==========================================");
        
        String imagePath = "D:\\workspace\\xprinter-backend\\data\\设备标识3（40_30）.png";
        File imageFile = new File(imagePath);
        
        if (!imageFile.exists()) {
            System.out.println("❌ 图片文件不存在: " + imagePath);
            return;
        }
        
        System.out.println("📸 图片信息:");
        System.out.println("  文件路径: " + imagePath);
        System.out.println("  文件大小: " + imageFile.length() + " bytes");
        
        try {
            // 直接调用TextIn API获取原始响应
            System.out.println("\n🌐 调用TextIn API...");
            TextInApiClient apiClient = TextInApiClient.me;
            
            // 模拟调用（这里需要真实的API调用来获取数据）
            // 由于测试环境可能没有配置，我们创建一个模拟的响应来演示问题
            TextInResponse mockResponse = createMockResponseForDeviceImage();
            
            System.out.println("📊 TextIn原始响应分析:");
            analyzeTextInResponse(mockResponse);
            
            System.out.println("\n🔄 ElementConverter处理过程:");
            ElementConverter converter = ElementConverter.me;
            OcrResponse result = converter.convertToXPrinterFormat(mockResponse, imageFile, 400, 300);
            
            System.out.println("\n📋 最终识别结果:");
            analyzeConversionResult(result);
            
        } catch (Exception e) {
            System.out.println("❌ 调试过程出错: " + e.getMessage());
            e.printStackTrace();
            
            // 如果API调用失败，分析可能的原因
            analyzePossibleIssues();
        }
    }
    
    /**
     * 分析TextIn的原始响应数据
     */
    @SuppressWarnings("unchecked")
    private void analyzeTextInResponse(TextInResponse response) {
        if (response == null || response.getResult() == null) {
            System.out.println("❌ TextIn响应为空");
            return;
        }
        
        Map<String, Object> result = response.getResult();
        
        // 分析detail数组
        List<Map<String, Object>> detail = (List<Map<String, Object>>) result.get("detail");
        System.out.println("  Detail数组: " + (detail != null ? detail.size() + "个元素" : "null"));
        if (detail != null) {
            for (int i = 0; i < detail.size(); i++) {
                Map<String, Object> item = detail.get(i);
                System.out.println("    [" + i + "] type: " + item.get("type") + 
                                 ", sub_type: " + item.get("sub_type"));
            }
        }
        
        // 分析pages数组
        List<Map<String, Object>> pages = (List<Map<String, Object>>) result.get("pages");
        if (pages != null && !pages.isEmpty()) {
            Map<String, Object> firstPage = pages.get(0);
            
            // 分析content数组
            List<Map<String, Object>> content = (List<Map<String, Object>>) firstPage.get("content");
            System.out.println("  Content数组: " + (content != null ? content.size() + "个元素" : "null"));
            if (content != null) {
                for (int i = 0; i < content.size(); i++) {
                    Map<String, Object> item = content.get(i);
                    String type = (String) item.get("type");
                    String subType = (String) item.get("sub_type");
                    String text = (String) item.get("text");
                    
                    System.out.println("    [" + i + "] id: " + item.get("id") + 
                                     ", type: " + type + 
                                     ", sub_type: " + subType + 
                                     ", text: '" + text + "'");
                                     
                    if ("image".equals(type)) {
                        System.out.println("      🎯 发现图像元素! sub_type: " + subType);
                        Map<String, Object> data = (Map<String, Object>) item.get("data");
                        if (data != null) {
                            System.out.println("      data字段: " + data.keySet());
                        }
                    }
                }
            }
            
            // 分析structured数组
            List<Map<String, Object>> structured = (List<Map<String, Object>>) firstPage.get("structured");
            System.out.println("  Structured数组: " + (structured != null ? structured.size() + "个元素" : "null"));
        }
    }
    
    /**
     * 分析转换结果
     */
    private void analyzeConversionResult(OcrResponse result) {
        if (result == null) {
            System.out.println("❌ 转换结果为空");
            return;
        }
        
        List<Map<String, Object>> elements = result.getElements();
        System.out.println("  总元素数量: " + (elements != null ? elements.size() : 0));
        
        if (elements != null) {
            int textCount = 0, barcodeCount = 0, qrcodeCount = 0, imageCount = 0, tableCount = 0;
            
            for (Map<String, Object> element : elements) {
                String elementType = (String) element.get("elementType");
                switch (elementType) {
                    case "1": textCount++; break;
                    case "2": barcodeCount++; break;
                    case "7": qrcodeCount++; break;
                    case "8": imageCount++; break;
                    case "10": tableCount++; break;
                }
            }
            
            System.out.println("  文本元素(1): " + textCount);
            System.out.println("  条形码(2): " + barcodeCount);
            System.out.println("  二维码(7): " + qrcodeCount);
            System.out.println("  🎯 图片元素(8): " + imageCount + " ⭐");
            System.out.println("  表格(10): " + tableCount);
            
            if (imageCount == 0) {
                System.out.println("\n❌ 问题确认: 没有生成图片元素!");
                System.out.println("可能原因分析:");
                System.out.println("1. TextIn API没有返回任何type=image的数据");
                System.out.println("2. 返回了type=image但全部是qrcode/barcode类型");
                System.out.println("3. type=image数据存在但imageUrl提取失败");
                System.out.println("4. 修复的default case没有生效");
            } else {
                System.out.println("\n✅ 成功识别到图片元素!");
                for (Map<String, Object> element : elements) {
                    if ("8".equals(element.get("elementType"))) {
                        System.out.println("  imageUrl: " + element.get("imageUrl"));
                        System.out.println("  imageType: " + element.get("imageType"));
                        System.out.println("  位置: (" + element.get("x") + "," + element.get("y") + ")");
                    }
                }
            }
        }
    }
    
    /**
     * 创建模拟的设备图片响应（基于实际可能的TextIn响应格式）
     */
    @SuppressWarnings("unchecked")
    private TextInResponse createMockResponseForDeviceImage() {
        System.out.println("📝 创建模拟的设备标识3响应数据...");
        System.out.println("  (实际情况中应该从TextIn API获取真实数据)");
        
        // 这里创建一个可能的响应格式，模拟设备标识3的识别结果
        // 实际的TextIn响应需要通过真实API调用获得
        
        // TODO: 这里需要真实的TextIn API调用来获取准确数据
        // 当前只是为了演示调试流程
        
        return new TextInResponse(); // 返回空响应，触发分析流程
    }
    
    /**
     * 分析可能的问题原因
     */
    private void analyzePossibleIssues() {
        System.out.println("\n🔍 可能的问题原因分析:");
        System.out.println("==========================================");
        
        System.out.println("1. TextIn API配置问题:");
        System.out.println("   - API密钥是否正确配置？");
        System.out.println("   - 应用ID是否有效？");
        System.out.println("   - 网络连接是否正常？");
        
        System.out.println("\n2. TextIn识别能力限制:");
        System.out.println("   - 该logo可能对TextIn来说太小或不够清晰");
        System.out.println("   - TextIn可能没有识别出China Tower logo为图像元素");
        System.out.println("   - 只识别出了文本内容，没有图像内容");
        
        System.out.println("\n3. 调试建议:");
        System.out.println("   - 启用详细日志查看TextIn原始响应");
        System.out.println("   - 尝试使用其他包含明显logo的图片测试");
        System.out.println("   - 检查TextIn API文档确认图像识别功能是否启用");
        System.out.println("   - 对比多多买菜图片(有明显彩色logo)的识别结果");
    }
}