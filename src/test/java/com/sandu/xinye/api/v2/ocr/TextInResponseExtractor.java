package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import com.alibaba.fastjson.JSON;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import java.io.File;

/**
 * TextIn响应结果提取器
 * 专门用于获取和展示TextIn API的原始响应数据
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class TextInResponseExtractor {
    
    @Test
    public void extractTextInResponseForDeviceImage() {
        System.out.println("==========================================");
        System.out.println("🔍 提取设备标识3的TextIn API响应结果");
        System.out.println("==========================================");
        
        try {
            // 初始化配置
            initializeConfig();
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\设备标识3（40_30）.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("❌ 图片文件不存在: " + imagePath);
                return;
            }
            
            System.out.println("📸 目标图片: " + imagePath);
            System.out.println("📏 文件大小: " + imageFile.length() + " bytes");
            System.out.println();
            
            // 调用TextIn API
            System.out.println("🌐 调用TextIn API...");
            TextInApiClient apiClient = TextInApiClient.me;
            TextInResponse response = apiClient.recognizeImage(imageFile);
            
            System.out.println("✅ API调用成功!");
            System.out.println();
            
            // 输出完整的JSON响应
            System.out.println("==========================================");
            System.out.println("📋 TextIn API 完整响应结果:");
            System.out.println("==========================================");
            
            String jsonResponse = JSON.toJSONString(response, true);
            System.out.println(jsonResponse);
            
            System.out.println();
            System.out.println("==========================================");
            System.out.println("📊 响应数据结构分析:");
            System.out.println("==========================================");
            
            analyzeResponse(response);
            
        } catch (Exception e) {
            System.out.println("❌ 获取TextIn响应时出错: " + e.getMessage());
            e.printStackTrace();
            
            System.out.println();
            System.out.println("💡 可能的原因:");
            System.out.println("1. TextIn API配置问题（检查textin.api.key和textin.app.id）");
            System.out.println("2. 网络连接问题");
            System.out.println("3. API密钥或应用ID无效");
            System.out.println("4. 配置文件未正确加载");
        }
    }
    
    @Test 
    public void extractTextInResponseForDuoduoImage() {
        System.out.println("==========================================");
        System.out.println("🔍 提取多多买菜的TextIn API响应结果（对比测试）");
        System.out.println("==========================================");
        
        try {
            // 初始化配置
            initializeConfig();
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\多多买菜（40_30）.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("❌ 图片文件不存在: " + imagePath);
                System.out.println("💡 尝试其他可能的路径...");
                
                // 尝试其他可能的文件名
                String[] possibleNames = {
                    "D:\\workspace\\xprinter-backend\\data\\多多买菜.png",
                    "D:\\workspace\\xprinter-backend\\data\\duoduo.png"
                };
                
                for (String path : possibleNames) {
                    File tryFile = new File(path);
                    if (tryFile.exists()) {
                        imageFile = tryFile;
                        imagePath = path;
                        System.out.println("✅ 找到图片: " + path);
                        break;
                    }
                }
                
                if (!imageFile.exists()) {
                    System.out.println("❌ 未找到多多买菜图片，跳过对比测试");
                    return;
                }
            }
            
            System.out.println("📸 目标图片: " + imagePath);
            System.out.println("📏 文件大小: " + imageFile.length() + " bytes");
            System.out.println();
            
            // 调用TextIn API
            System.out.println("🌐 调用TextIn API...");
            TextInApiClient apiClient = TextInApiClient.me;
            TextInResponse response = apiClient.recognizeImage(imageFile);
            
            System.out.println("✅ API调用成功!");
            System.out.println();
            
            // 输出完整的JSON响应
            System.out.println("==========================================");
            System.out.println("📋 多多买菜 TextIn API 完整响应结果:");
            System.out.println("==========================================");
            
            String jsonResponse = JSON.toJSONString(response, true);
            System.out.println(jsonResponse);
            
            System.out.println();
            System.out.println("==========================================");
            System.out.println("📊 多多买菜响应数据结构分析:");
            System.out.println("==========================================");
            
            analyzeResponse(response);
            
        } catch (Exception e) {
            System.out.println("❌ 获取多多买菜TextIn响应时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 初始化配置
     */
    private void initializeConfig() {
        try {
            // 尝试加载配置文件
            PropKit.use("common_config.txt");
            System.out.println("✅ 配置文件加载成功");
        } catch (Exception e) {
            System.out.println("⚠️ 配置文件加载可能有问题: " + e.getMessage());
            System.out.println("   请确保在开发环境中正确配置了TextIn API");
        }
    }
    
    /**
     * 分析TextIn响应结构
     */
    @SuppressWarnings("unchecked")
    private void analyzeResponse(TextInResponse response) {
        if (response == null) {
            System.out.println("❌ 响应为空");
            return;
        }
        
        System.out.println("📋 基本信息:");
        System.out.println("  响应码: " + response.getCode());
        System.out.println("  消息: " + response.getMessage());
        System.out.println("  状态: " + (response.getCode() == 200 ? "✅ 成功" : "❌ 失败"));
        
        if (response.getResult() == null) {
            System.out.println("❌ result字段为空");
            return;
        }
        
        java.util.Map<String, Object> result = response.getResult();
        
        // 分析detail数组
        java.util.List<java.util.Map<String, Object>> detail = 
            (java.util.List<java.util.Map<String, Object>>) result.get("detail");
        
        System.out.println();
        System.out.println("📄 Detail数组:");
        if (detail == null) {
            System.out.println("  ❌ detail字段不存在");
        } else if (detail.isEmpty()) {
            System.out.println("  ⚠️ detail数组为空");
        } else {
            System.out.println("  ✅ detail数组包含 " + detail.size() + " 个元素");
            for (int i = 0; i < detail.size(); i++) {
                java.util.Map<String, Object> item = detail.get(i);
                System.out.println("    [" + i + "] type: " + item.get("type") + 
                                 ", sub_type: " + item.get("sub_type"));
            }
        }
        
        // 分析pages数组
        java.util.List<java.util.Map<String, Object>> pages = 
            (java.util.List<java.util.Map<String, Object>>) result.get("pages");
        
        System.out.println();
        System.out.println("📄 Pages数组:");
        if (pages == null || pages.isEmpty()) {
            System.out.println("  ❌ pages数组为空");
            return;
        }
        
        System.out.println("  ✅ pages数组包含 " + pages.size() + " 个页面");
        
        java.util.Map<String, Object> firstPage = pages.get(0);
        
        // 分析content数组
        java.util.List<java.util.Map<String, Object>> content = 
            (java.util.List<java.util.Map<String, Object>>) firstPage.get("content");
        
        System.out.println();
        System.out.println("📋 Content数组:");
        if (content == null) {
            System.out.println("  ❌ content字段不存在");
        } else if (content.isEmpty()) {
            System.out.println("  ⚠️ content数组为空");
        } else {
            System.out.println("  ✅ content数组包含 " + content.size() + " 个元素");
            
            int imageCount = 0;
            java.util.Map<String, Integer> typeCount = new java.util.HashMap<>();
            java.util.Map<String, Integer> subTypeCount = new java.util.HashMap<>();
            
            for (int i = 0; i < content.size(); i++) {
                java.util.Map<String, Object> item = content.get(i);
                String type = (String) item.get("type");
                String subType = (String) item.get("sub_type");
                String text = (String) item.get("text");
                
                // 统计类型
                typeCount.put(type != null ? type : "null", 
                            typeCount.getOrDefault(type, 0) + 1);
                
                if ("image".equals(type)) {
                    imageCount++;
                    subTypeCount.put(subType != null ? subType : "null",
                                   subTypeCount.getOrDefault(subType, 0) + 1);
                    
                    System.out.println("    🖼️ [" + i + "] IMAGE元素:");
                    System.out.println("        id: " + item.get("id"));
                    System.out.println("        sub_type: " + subType);
                    System.out.println("        text: " + (text != null ? "'" + text + "'" : "null"));
                    
                    // 检查data字段
                    java.util.Map<String, Object> data = 
                        (java.util.Map<String, Object>) item.get("data");
                    if (data != null) {
                        System.out.println("        data字段: " + data.keySet());
                        if (data.containsKey("image_url")) {
                            System.out.println("        image_url: " + data.get("image_url"));
                        }
                    }
                    
                    // 检查位置
                    java.util.List<Integer> pos = (java.util.List<Integer>) item.get("pos");
                    if (pos != null && pos.size() >= 8) {
                        int minX = Math.min(Math.min(pos.get(0), pos.get(2)), 
                                          Math.min(pos.get(4), pos.get(6)));
                        int minY = Math.min(Math.min(pos.get(1), pos.get(3)), 
                                          Math.min(pos.get(5), pos.get(7)));
                        int maxX = Math.max(Math.max(pos.get(0), pos.get(2)), 
                                          Math.max(pos.get(4), pos.get(6)));
                        int maxY = Math.max(Math.max(pos.get(1), pos.get(3)), 
                                          Math.max(pos.get(5), pos.get(7)));
                        System.out.println("        位置: (" + minX + "," + minY + ") " +
                                         "尺寸: " + (maxX-minX) + "x" + (maxY-minY));
                    }
                    System.out.println();
                } else if (!"line".equals(type)) {
                    // 显示非文本的其他元素
                    System.out.println("    📄 [" + i + "] " + type + "元素: " + 
                                     (text != null && text.length() > 50 ? 
                                      text.substring(0, 47) + "..." : text));
                }
            }
            
            System.out.println();
            System.out.println("📊 元素类型统计:");
            System.out.println("  类型分布: " + typeCount);
            System.out.println("  图像元素数量: " + imageCount);
            if (imageCount > 0) {
                System.out.println("  图像sub_type分布: " + subTypeCount);
                
                // 分析哪些会变成elementType=8
                int willBecomeImageElements = 0;
                for (java.util.Map.Entry<String, Integer> entry : subTypeCount.entrySet()) {
                    String subType = entry.getKey();
                    int count = entry.getValue();
                    if (!"qrcode".equals(subType) && !"barcode".equals(subType)) {
                        willBecomeImageElements += count;
                    }
                }
                
                System.out.println("  🎯 预计生成elementType=8的数量: " + willBecomeImageElements);
            }
        }
        
        // 分析structured数组
        java.util.List<java.util.Map<String, Object>> structured = 
            (java.util.List<java.util.Map<String, Object>>) firstPage.get("structured");
        
        System.out.println();
        System.out.println("🏗️ Structured数组:");
        if (structured == null) {
            System.out.println("  ❌ structured字段不存在");
        } else if (structured.isEmpty()) {
            System.out.println("  ⚠️ structured数组为空");
        } else {
            System.out.println("  ✅ structured数组包含 " + structured.size() + " 个结构化元素");
            for (int i = 0; i < structured.size(); i++) {
                java.util.Map<String, Object> item = structured.get(i);
                System.out.println("    [" + i + "] type: " + item.get("type"));
            }
        }
    }
}