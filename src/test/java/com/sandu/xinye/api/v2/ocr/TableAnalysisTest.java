package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 表格识别分析测试
 * 分析TextIn对表格的识别结果
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class TableAnalysisTest {
    
    private OcrService ocrService;
    private TextInApiClient textInApiClient;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        ocrService = OcrService.me;
        textInApiClient = TextInApiClient.me;
    }
    
    @Test
    public void analyzeShoeTableRecognition() {
        try {
            System.out.println("📋 分析鞋子表格的TextIn原始返回数据...");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\鞋子（60_40）-2.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 1. 直接调用TextIn API获取原始数据
            System.out.println("🔍 调用TextIn API获取原始数据...");
            TextInResponse rawResponse = textInApiClient.recognizeImage(imageFile);
            
            if (rawResponse == null) {
                System.out.println("❌ TextIn API调用失败");
                return;
            }
            
            // 2. 分析原始返回结构
            analyzeTextInRawResponse(rawResponse);
            
            // 3. 调用OCR服务查看转换结果
            System.out.println("\n🔄 调用OCR服务查看转换结果...");
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "鞋子（60_40）-2.png");
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (result.isTrue("success")) {
                OcrResponse ocrResponse = (OcrResponse) result.get("data");
                analyzeOcrResponse(ocrResponse);
            } else {
                System.out.println("❌ OCR服务调用失败: " + result.getStr("msg"));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 分析TextIn的原始返回数据
     */
    @SuppressWarnings("unchecked")
    private void analyzeTextInRawResponse(TextInResponse rawResponse) {
        System.out.println("\n📊 TextIn原始数据分析:");
        System.out.println("Result: " + rawResponse.getResult());
        
        List<Map<String, Object>> pages = rawResponse.getPages();
        if (pages == null || pages.isEmpty()) {
            System.out.println("❌ 没有页面数据");
            return;
        }
        
        Map<String, Object> firstPage = pages.get(0);
        System.out.println("页面字段: " + firstPage.keySet());
        
        // 分析content数组
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) firstPage.get("content");
        if (contentList != null) {
            System.out.println("\n📝 Content数组 (共" + contentList.size() + "个元素):");
            for (int i = 0; i < contentList.size(); i++) {
                Map<String, Object> item = contentList.get(i);
                System.out.println("  [" + i + "] type=" + item.get("type") + 
                                 ", text='" + item.get("text") + 
                                 "', sub_type=" + item.get("sub_type"));
                
                // 如果是表格类型，详细分析
                if ("table".equals(item.get("type"))) {
                    analyzeTableItem(item);
                }
            }
        }
        
        // 分析structured数组
        List<Map<String, Object>> structuredList = (List<Map<String, Object>>) firstPage.get("structured");
        if (structuredList != null) {
            System.out.println("\n🏗️ Structured数组 (共" + structuredList.size() + "个元素):");
            for (int i = 0; i < structuredList.size(); i++) {
                Map<String, Object> item = structuredList.get(i);
                System.out.println("  [" + i + "] type=" + item.get("type") + 
                                 ", sub_type=" + item.get("sub_type"));
                
                // 如果是表格类型，详细分析
                if ("table".equals(item.get("type"))) {
                    analyzeTableItem(item);
                }
            }
        }
    }
    
    /**
     * 详细分析表格元素
     */
    @SuppressWarnings("unchecked")
    private void analyzeTableItem(Map<String, Object> tableItem) {
        System.out.println("    📋 表格详情:");
        System.out.println("      - ID: " + tableItem.get("id"));
        System.out.println("      - 位置: " + tableItem.get("pos"));
        
        // 分析表格单元格
        List<Map<String, Object>> cells = (List<Map<String, Object>>) tableItem.get("cells");
        if (cells != null) {
            System.out.println("      - 单元格数量: " + cells.size());
            for (int i = 0; i < Math.min(cells.size(), 10); i++) {  // 最多显示前10个单元格
                Map<String, Object> cell = cells.get(i);
                System.out.println("        单元格[" + i + "]: row=" + cell.get("row") + 
                                 ", col=" + cell.get("col") + 
                                 ", text='" + cell.get("text") + "'");
            }
        } else {
            System.out.println("      - ❌ 没有cells数据");
        }
    }
    
    /**
     * 分析OCR转换后的结果
     */
    private void analyzeOcrResponse(OcrResponse ocrResponse) {
        System.out.println("\n📄 OCR转换结果分析:");
        List<Map<String, Object>> elements = ocrResponse.getElements();
        System.out.println("元素总数: " + elements.size());
        
        boolean foundTable = false;
        for (int i = 0; i < elements.size(); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));
            
            System.out.println("  [" + i + "] type=" + elementType + 
                             ", content='" + element.get("content") + "'");
            
            if ("10".equals(elementType)) {
                foundTable = true;
                System.out.println("    ✅ 发现表格元素!");
                System.out.println("      - 行数: " + element.get("rowCount"));
                System.out.println("      - 列数: " + element.get("colCount"));
                
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> cells = (List<Map<String, Object>>) element.get("cells");
                if (cells != null) {
                    System.out.println("      - 单元格数量: " + cells.size());
                    for (Map<String, Object> cell : cells) {
                        System.out.println("        (" + cell.get("row") + "," + cell.get("col") + 
                                         "): '" + cell.get("content") + "'");
                    }
                }
            }
        }
        
        if (!foundTable) {
            System.out.println("❌ 没有找到表格元素 (elementType=10)");
            System.out.println("可能的问题: TextIn没有将其识别为表格，或转换逻辑有问题");
        }
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("table_analysis_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}