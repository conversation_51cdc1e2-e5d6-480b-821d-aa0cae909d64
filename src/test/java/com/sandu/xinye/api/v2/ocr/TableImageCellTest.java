package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 表格单元格图片检测测试
 * 使用包含图片单元格的测试图片验证hasImage属性是否正确标记
 * 
 * <AUTHOR>  
 * @date 2025-08-08
 */
public class TableImageCellTest {
    
    private OcrService ocrService;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        ocrService = OcrService.me;
    }
    
    @Test
    public void testTableWithImageCell() {
        try {
            System.out.println("🖼️ 测试表格中的图片单元格检测");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\表格单元格带图片.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 使用新架构进行OCR
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "表格单元格带图片.png");
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (!result.isTrue("success")) {
                System.out.println("❌ OCR服务调用失败: " + result.getStr("msg"));
                return;
            }
            
            OcrResponse ocrResponse = (OcrResponse) result.get("data");
            analyzeTableWithImages(ocrResponse);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 分析包含图片的表格单元格
     */
    @SuppressWarnings("unchecked")
    private void analyzeTableWithImages(OcrResponse ocrResponse) {
        System.out.println("\n📊 表格图片单元格分析:");
        
        List<Map<String, Object>> elements = ocrResponse.getElements();
        boolean foundTable = false;
        int imageCellCount = 0;
        int textCellCount = 0;
        
        for (Map<String, Object> element : elements) {
            String elementType = String.valueOf(element.get("elementType"));
            
            if ("10".equals(elementType)) { // 表格类型
                foundTable = true;
                System.out.println("✅ 发现表格元素");
                System.out.println("  行数: " + element.get("rowCount"));
                System.out.println("  列数: " + element.get("colCount"));
                
                List<Map<String, Object>> cells = (List<Map<String, Object>>) element.get("cells");
                if (cells != null && !cells.isEmpty()) {
                    System.out.println("  单元格数量: " + cells.size());
                    
                    System.out.println("\n📋 单元格图片检测详情:");
                    for (int i = 0; i < cells.size(); i++) {
                        Map<String, Object> cell = cells.get(i);
                        
                        String content = String.valueOf(cell.get("content"));
                        Integer row = (Integer) cell.get("row");
                        Integer col = (Integer) cell.get("col");
                        Boolean hasImage = (Boolean) cell.get("hasImage");
                        
                        System.out.println("  单元格[" + i + "] (" + row + "," + col + "):");
                        System.out.println("    内容: '" + (content.length() > 80 ? content.substring(0, 77) + "..." : content) + "'");
                        System.out.println("    hasImage: " + hasImage);
                        
                        if (hasImage != null && hasImage) {
                            imageCellCount++;
                            System.out.println("    🖼️ 检测到图片内容!");
                            
                            // 分析图片内容的具体格式
                            analyzeImageContentFormat(content);
                        } else {
                            textCellCount++;
                            System.out.println("    📝 文本内容");
                        }
                        
                        // 显示其他样式信息
                        String alignment = (String) cell.get("alignment");
                        Boolean bold = (Boolean) cell.get("bold");
                        Boolean italic = (Boolean) cell.get("italic");
                        Integer width = (Integer) cell.get("width");
                        Integer height = (Integer) cell.get("height");
                        Double charWidth = (Double) cell.get("charWidth");
                        
                        System.out.println("    样式: alignment=" + alignment + ", bold=" + bold + 
                                         ", italic=" + italic + ", size=" + width + "x" + height + 
                                         ", charWidth=" + charWidth);
                        System.out.println();
                    }
                    
                    // 统计结果
                    System.out.println("🔍 检测结果统计:");
                    System.out.println("  包含图片的单元格: " + imageCellCount + " 个");
                    System.out.println("  包含文本的单元格: " + textCellCount + " 个");
                    System.out.println("  总单元格数量: " + cells.size() + " 个");
                    
                    if (imageCellCount > 0) {
                        System.out.println("✅ 图片检测功能工作正常！");
                    } else {
                        System.out.println("⚠️ 未检测到图片单元格，可能需要检查检测逻辑");
                    }
                    
                } else {
                    System.out.println("❌ 表格没有单元格数据");
                }
            }
        }
        
        if (!foundTable) {
            System.out.println("❌ 未发现表格元素");
        }
    }
    
    /**
     * 分析图片内容的具体格式
     */
    private void analyzeImageContentFormat(String content) {
        System.out.println("      🔍 图片内容格式分析:");
        
        if (content.contains("<img")) {
            System.out.println("        - 包含HTML img标签");
        }
        if (content.contains("web-api.textin.com")) {
            System.out.println("        - 包含TextIn图片服务URL");
        }
        if (content.contains("ocr_image")) {
            System.out.println("        - 包含OCR图片标识");
        }
        if (content.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp).*")) {
            System.out.println("        - 包含图片文件扩展名");
        }
        if (content.matches(".*https?://.*")) {
            System.out.println("        - 包含HTTP/HTTPS链接");
        }
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("table_image_test_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}