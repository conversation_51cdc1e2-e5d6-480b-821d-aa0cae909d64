package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import static org.junit.Assert.*;

import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import java.util.Map;
import java.util.List;

/**
 * 图片元素集成测试
 * 测试真实图片的识别功能
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class ImageElementIntegrationTest {
    
    @Test
    public void testImageElementDataStructure() {
        try {
            System.out.println("测试图片元素数据结构和功能...");
            
            // 测试基本功能，而非实际的API调用
            testImageElementCreation();
            testImageSubTypeMapping();
            testImageUrlExtraction();
            
            System.out.println("✅ 图片元素功能测试完成!");
            
        } catch (Exception e) {
            e.printStackTrace();
            fail("图片元素功能测试异常: " + e.getMessage());
        }
    }
    
    private void testImageElementCreation() {
        System.out.println("测试图片元素创建...");
        
        OcrResponse response = new OcrResponse();
        response.addImageElement(150, 50, 80, 60, 
                               "https://web-api.textin.com/ocr_image/external/logo_123.png",
                               "logo", 0.92, true);
        
        List<Map<String, Object>> elements = response.getElements();
        assertEquals("应该有1个图片元素", 1, elements.size());
        
        Map<String, Object> imageElement = elements.get(0);
        assertEquals("elementType应为8", "8", imageElement.get("elementType"));
        assertEquals("imageType应为logo", "logo", imageElement.get("imageType"));
        assertTrue("confidence应在合理范围", (Double) imageElement.get("confidence") > 0.9);
        
        System.out.println("图片元素创建测试通过: " + imageElement);
    }
    
    private void testImageSubTypeMapping() {
        System.out.println("测试图片子类型映射...");
        
        ElementConverter converter = ElementConverter.me;
        
        try {
            java.lang.reflect.Method method = ElementConverter.class.getDeclaredMethod("mapImageSubType", String.class);
            method.setAccessible(true);
            
            assertEquals("logo应映射为logo", "logo", method.invoke(converter, "logo"));
            assertEquals("stamp应映射为stamp", "stamp", method.invoke(converter, "stamp"));
            assertEquals("chart应映射为chart", "chart", method.invoke(converter, "chart"));
            
            System.out.println("图片子类型映射测试通过");
            
        } catch (Exception e) {
            fail("图片子类型映射测试失败: " + e.getMessage());
        }
    }
    
    private void testImageUrlExtraction() {
        System.out.println("测试图片URL提取...");
        
        ElementConverter converter = ElementConverter.me;
        
        try {
            java.lang.reflect.Method method = ElementConverter.class.getDeclaredMethod("extractUrlFromImgTag", String.class);
            method.setAccessible(true);
            
            // 测试典型的TextIn返回格式
            String textinImgTag = "<img src=\"https://web-api.textin.com/ocr_image/external/device_logo_456.jpg\">";
            String extractedUrl = (String) method.invoke(converter, textinImgTag);
            
            assertNotNull("应提取到URL", extractedUrl);
            assertTrue("URL应包含textin.com", extractedUrl.contains("textin.com"));
            assertTrue("URL应包含device_logo", extractedUrl.contains("device_logo"));
            
            System.out.println("提取的URL: " + extractedUrl);
            System.out.println("图片URL提取测试通过");
            
        } catch (Exception e) {
            fail("图片URL提取测试失败: " + e.getMessage());
        }
    }
    
}