package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.Map;

/**
 * Bold识别测试
 * 验证修复后的bold识别功能能正确处理text_title类型
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class BoldRecognitionTest {
    
    private TextInApiClient textInApiClient;
    private ElementConverter elementConverter;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        textInApiClient = TextInApiClient.me;
        elementConverter = ElementConverter.me;
    }
    
    @Test
    public void testBoldRecognitionWithRealImage() {
        try {
            // 使用真实的测试图片
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\咖啡粉（60_40）.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("测试图片不存在，跳过测试: " + imagePath);
                return;
            }
            
            System.out.println("开始Bold识别测试...");
            
            // 1. 调用TextIn API
            TextInResponse textInResponse = textInApiClient.recognizeImage(imageFile);
            
            // 2. 转换为XPrinter格式
            OcrResponse ocrResponse = elementConverter.convertToXPrinterFormat(
                textInResponse, imageFile, 480, 360);
            
            System.out.println("\n=== Bold识别测试结果 ===");
            System.out.println("识别元素总数: " + ocrResponse.getElements().size());
            
            // 检查每个文本元素的bold属性
            boolean foundBoldTitle = false;
            int textElementCount = 0;
            
            for (Map<String, Object> element : ocrResponse.getElements()) {
                String elementType = String.valueOf(element.get("elementType"));
                
                // 只检查文本元素 (elementType = 1)
                if ("1".equals(elementType)) {
                    textElementCount++;
                    String content = (String) element.get("content");
                    Boolean bold = (Boolean) element.get("bold");
                    
                    System.out.println("文本元素: '" + content + "' - Bold: " + bold);
                    
                    // 检查标题是否被正确识别为bold  
                    if (content != null && content.contains("咖啡粉") && Boolean.TRUE.equals(bold)) {
                        foundBoldTitle = true;
                        System.out.println("✅ 标题正确识别为Bold!");
                    }
                }
            }
            
            System.out.println("\n=== 测试结果总结 ===");
            System.out.println("文本元素数量: " + textElementCount);
            System.out.println("标题Bold识别: " + (foundBoldTitle ? "✅ 成功" : "❌ 失败"));
            
            if (!foundBoldTitle) {
                System.out.println("⚠️  标题应该被识别为Bold，但实际没有！");
            }
            
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}