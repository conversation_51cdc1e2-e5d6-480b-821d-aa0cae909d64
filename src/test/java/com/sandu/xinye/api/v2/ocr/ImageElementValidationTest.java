package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import static org.junit.Assert.*;

import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

/**
 * 图片元素验证测试
 * 使用模拟数据验证图片元素处理的修复效果
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class ImageElementValidationTest {
    
    @Test
    public void testImageElementHandling_FixedDefaultCase() {
        System.out.println("🎯 测试图片元素处理修复效果");
        
        // 创建模拟的TextIn响应数据
        TextInResponse mockResponse = createMockTextInResponseWithImages();
        
        // 创建ElementConverter实例
        ElementConverter converter = ElementConverter.me;
        
        // 创建模拟图片文件
        File mockImageFile = new File("test.png");
        
        // 执行转换
        OcrResponse result = converter.convertToXPrinterFormat(mockResponse, mockImageFile, 400, 300);
        
        // 验证结果
        assertNotNull("转换结果不能为空", result);
        assertNotNull("元素列表不能为空", result.getElements());
        
        List<Map<String, Object>> elements = result.getElements();
        System.out.println("识别到的元素数量: " + elements.size());
        
        // 统计各类型元素
        int textCount = 0, barcodeCount = 0, qrcodeCount = 0, imageCount = 0;
        
        for (Map<String, Object> element : elements) {
            String elementType = (String) element.get("elementType");
            System.out.println("元素类型: " + elementType + ", 内容: " + element.get("content") + 
                             ", imageType: " + element.get("imageType"));
            
            switch (elementType) {
                case "1": textCount++; break;
                case "2": barcodeCount++; break; 
                case "7": qrcodeCount++; break;
                case "8": imageCount++; break;
            }
        }
        
        System.out.println("文本元素: " + textCount);
        System.out.println("条形码: " + barcodeCount);
        System.out.println("二维码: " + qrcodeCount);
        System.out.println("图片元素: " + imageCount + " ⭐");
        
        // 关键验证：应该识别到图片元素
        assertTrue("修复后应该能识别到图片元素", imageCount > 0);
        
        // 验证图片元素的属性
        boolean foundImageElement = false;
        for (Map<String, Object> element : elements) {
            if ("8".equals(element.get("elementType"))) {
                foundImageElement = true;
                
                // 验证必要属性存在
                assertNotNull("图片元素应该有imageUrl", element.get("imageUrl"));
                assertNotNull("图片元素应该有imageType", element.get("imageType"));
                assertNotNull("图片元素应该有confidence", element.get("confidence"));
                assertNotNull("图片元素应该有位置信息", element.get("x"));
                assertNotNull("图片元素应该有位置信息", element.get("y"));
                assertNotNull("图片元素应该有尺寸信息", element.get("width"));
                assertNotNull("图片元素应该有尺寸信息", element.get("height"));
                
                System.out.println("✅ 图片元素验证通过:");
                System.out.println("  imageUrl: " + element.get("imageUrl"));
                System.out.println("  imageType: " + element.get("imageType"));
                System.out.println("  confidence: " + element.get("confidence"));
                System.out.println("  位置: (" + element.get("x") + "," + element.get("y") + 
                                 ") 尺寸: " + element.get("width") + "x" + element.get("height"));
                break;
            }
        }
        
        assertTrue("应该找到至少一个图片元素", foundImageElement);
        
        System.out.println("🎉 图片元素处理修复验证成功!");
    }
    
    /**
     * 创建包含图片元素的模拟TextIn响应数据
     * 模拟真实场景：包含已知的qrcode、barcode，以及未知的图片类型
     */
    @SuppressWarnings("unchecked")
    private TextInResponse createMockTextInResponseWithImages() {
        TextInResponse response = new TextInResponse();
        
        // 创建result对象
        Map<String, Object> result = new HashMap<>();
        
        // 创建页面数据
        Map<String, Object> page = new HashMap<>();
        
        // 创建content数组
        List<Map<String, Object>> contentList = new ArrayList<>();
        
        // 1. 添加文本元素
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("id", 1);
        textContent.put("type", "line");
        textContent.put("text", "测试文本");
        textContent.put("pos", Arrays.asList(10, 10, 100, 10, 100, 30, 10, 30));
        contentList.add(textContent);
        
        // 2. 添加qrcode图像（已知类型）
        Map<String, Object> qrcodeContent = new HashMap<>();
        qrcodeContent.put("id", 2);
        qrcodeContent.put("type", "image");
        qrcodeContent.put("sub_type", "qrcode");
        qrcodeContent.put("pos", Arrays.asList(50, 50, 150, 50, 150, 150, 50, 150));
        Map<String, Object> qrcodeData = new HashMap<>();
        qrcodeData.put("content", "https://example.com");
        qrcodeContent.put("data", qrcodeData);
        contentList.add(qrcodeContent);
        
        // 3. 添加barcode图像（已知类型）
        Map<String, Object> barcodeContent = new HashMap<>();
        barcodeContent.put("id", 3);
        barcodeContent.put("type", "image");
        barcodeContent.put("sub_type", "barcode");
        barcodeContent.put("pos", Arrays.asList(200, 50, 350, 50, 350, 100, 200, 100));
        contentList.add(barcodeContent);
        
        // 4. 关键测试：添加未知的图像类型（这是修复的重点）
        Map<String, Object> unknownImageContent = new HashMap<>();
        unknownImageContent.put("id", 4);
        unknownImageContent.put("type", "image");
        unknownImageContent.put("sub_type", "unknown_logo");  // 未知类型
        unknownImageContent.put("pos", Arrays.asList(100, 200, 200, 200, 200, 300, 100, 300));
        Map<String, Object> unknownImageData = new HashMap<>();
        unknownImageData.put("image_url", "https://example.com/logo.png");
        unknownImageContent.put("data", unknownImageData);
        contentList.add(unknownImageContent);
        
        // 5. 添加另一个未知图像类型
        Map<String, Object> anotherUnknownContent = new HashMap<>();
        anotherUnknownContent.put("id", 5);
        anotherUnknownContent.put("type", "image");
        anotherUnknownContent.put("sub_type", "weird_type");  // 另一个未知类型
        anotherUnknownContent.put("pos", Arrays.asList(250, 200, 350, 200, 350, 300, 250, 300));
        Map<String, Object> anotherImageData = new HashMap<>();
        anotherImageData.put("image_url", "https://example.com/icon.png");
        anotherUnknownContent.put("data", anotherImageData);
        contentList.add(anotherUnknownContent);
        
        page.put("content", contentList);
        page.put("structured", new ArrayList<>());
        
        result.put("pages", Arrays.asList(page));
        result.put("detail", new ArrayList<>());  // 使用空detail，触发降级处理
        
        response.setResult(result);
        
        System.out.println("📊 创建的模拟数据包含:");
        System.out.println("- 1个文本元素");
        System.out.println("- 1个qrcode图像（已知类型）");
        System.out.println("- 1个barcode图像（已知类型）");
        System.out.println("- 2个未知类型图像（unknown_logo, weird_type）");
        System.out.println("修复前：未知类型会被跳过");
        System.out.println("修复后：未知类型应该被处理为图片元素");
        System.out.println();
        
        return response;
    }
}