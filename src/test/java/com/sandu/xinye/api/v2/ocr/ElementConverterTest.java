package com.sandu.xinye.api.v2.ocr;

import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 元素转换器单元测试
 * 简化版本，专注于核心功能测试
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ElementConverterTest {

    private ElementConverter converter;
    private File testImageFile;

    @Before
    public void setUp() {
        converter = ElementConverter.me;
        // 创建一个简单的测试文件对象
        testImageFile = new File("test.jpg");
    }
    
    @Test
    public void testConvertToXPrinterFormat_NullData() {
        // 测试空数据转换
        TextInResponse response = new TextInResponse();
        response.setResult(null);

        OcrResponse result = converter.convertToXPrinterFormat(response, testImageFile, 800, 600);

        assertNotNull(result);
        assertNotNull(result.getImageInfo());
        assertEquals(800, result.getImageInfo().get("width"));
        assertEquals(600, result.getImageInfo().get("height"));
        assertEquals("jpg", result.getImageInfo().get("format"));
        assertTrue(result.getElements().isEmpty());
    }
    
    @Test
    public void testConvertToXPrinterFormat_WithRealTextInFormat() {
        // 测试真实的TextIn API格式
        TextInResponse response = createRealTextInResponse();

        OcrResponse result = converter.convertToXPrinterFormat(response, testImageFile, 800, 600);

        assertNotNull(result);
        assertTrue(result.getElements().size() > 0);

        // 验证至少有一个文本元素
        boolean hasTextElement = false;
        for (Map<String, Object> element : result.getElements()) {
            if ("1".equals(element.get("elementType"))) {
                hasTextElement = true;
                assertNotNull(element.get("content"));
                assertNotNull(element.get("x"));
                assertNotNull(element.get("y"));
                assertNotNull(element.get("width"));
                assertNotNull(element.get("height"));
                assertNotNull(element.get("textSize"));
                break;
            }
        }
        assertTrue("应该包含至少一个文本元素", hasTextElement);
    }
    
    // 简化测试，专注于核心功能验证

    // 辅助方法：创建真实的TextIn API响应格式
    private TextInResponse createRealTextInResponse() {
        TextInResponse response = new TextInResponse();
        response.setCode(200);
        response.setMessage("success");

        // 创建result数据
        Map<String, Object> result = new HashMap<>();
        result.put("total_page_number", 1);
        result.put("valid_page_number", 1);

        // 创建页面数据
        List<Map<String, Object>> pages = new ArrayList<>();
        Map<String, Object> page = new HashMap<>();
        page.put("status", "success");
        page.put("page_id", 0);
        page.put("width", 800);
        page.put("height", 600);

        // 创建raw_ocr数据
        List<Map<String, Object>> rawOcr = new ArrayList<>();
        Map<String, Object> ocrItem = new HashMap<>();
        ocrItem.put("text", "测试文本");
        ocrItem.put("type", "text");
        ocrItem.put("score", 0.99);

        // 创建position数组 [x1, y1, x2, y2, x3, y3, x4, y4]
        List<Integer> position = new ArrayList<>();
        position.add(10); // x1
        position.add(10); // y1
        position.add(100); // x2
        position.add(10); // y2
        position.add(100); // x3
        position.add(50); // y3
        position.add(10); // x4
        position.add(50); // y4
        ocrItem.put("position", position);

        rawOcr.add(ocrItem);
        page.put("raw_ocr", rawOcr);

        pages.add(page);
        result.put("pages", pages);

        response.setResult(result);
        return response;
    }
}
