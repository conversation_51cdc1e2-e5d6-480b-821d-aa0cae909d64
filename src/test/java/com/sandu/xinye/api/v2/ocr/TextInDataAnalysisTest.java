package com.sandu.xinye.api.v2.ocr;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * TextIn API 数据结构分析测试
 * 用于分析TextIn API对data/orc.png返回的原始数据结构
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class TextInDataAnalysisTest {
    
    private TextInApiClient textInApiClient;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        textInApiClient = TextInApiClient.me;
    }
    
    @Test
    public void analyzeOrcImageResponse() {
        try {
            // 使用问题图片
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\orc.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("❌ 测试图片不存在: " + imagePath);
                return;
            }
            
            System.out.println("🔍 开始分析TextIn API对orc.png的返回数据...");
            System.out.println("📁 图片路径: " + imagePath);
            System.out.println("📊 图片大小: " + imageFile.length() + " bytes");
            
            // 调用TextIn API
            TextInResponse textInResponse = textInApiClient.recognizeImage(imageFile);
            
            System.out.println("\n=== TextIn API 基础响应信息 ===");
            System.out.println("响应代码: " + textInResponse.getCode());
            System.out.println("响应消息: " + textInResponse.getMessage());
            
            if (textInResponse.getPages() != null && !textInResponse.getPages().isEmpty()) {
                System.out.println("页面数量: " + textInResponse.getPages().size());
                
                // 分析第一页数据
                Map<String, Object> firstPage = textInResponse.getPages().get(0);
                analyzePageStructure(firstPage);
                
            } else {
                System.out.println("❌ 没有返回页面数据");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 分析过程异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @SuppressWarnings("unchecked")
    private void analyzePageStructure(Map<String, Object> pageData) {
        System.out.println("\n=== 页面数据结构分析 ===");
        System.out.println("页面数据的顶层字段: " + pageData.keySet());
        
        // 分析content数组
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) pageData.get("content");
        if (contentList != null && !contentList.isEmpty()) {
            System.out.println("\n📝 Content数组分析:");
            System.out.println("content元素总数: " + contentList.size());
            
            for (int i = 0; i < contentList.size(); i++) {
                Map<String, Object> item = contentList.get(i);
                analyzeContentItem(i, item);
            }
        }
        
        // 分析structured数组
        List<Map<String, Object>> structuredList = (List<Map<String, Object>>) pageData.get("structured");
        if (structuredList != null && !structuredList.isEmpty()) {
            System.out.println("\n🏗️ Structured数组分析:");
            System.out.println("structured元素总数: " + structuredList.size());
            
            for (int i = 0; i < structuredList.size(); i++) {
                Map<String, Object> item = structuredList.get(i);
                analyzeStructuredItem(i, item);
            }
        }
    }
    
    private void analyzeContentItem(int index, Map<String, Object> item) {
        String type = (String) item.get("type");
        String text = (String) item.get("text");
        String subType = (String) item.get("sub_type");
        Integer id = (Integer) item.get("id");
        List<Integer> pos = (List<Integer>) item.get("pos");
        
        System.out.println("\n  Content[" + index + "]:");
        System.out.println("    id: " + id);
        System.out.println("    type: " + type);
        System.out.println("    sub_type: " + subType);
        System.out.println("    text: '" + text + "'");
        System.out.println("    pos: " + pos);
        System.out.println("    所有字段: " + item.keySet());
        
        // 特别关注image类型的元素（可能是条码/二维码）
        if ("image".equals(type)) {
            System.out.println("    ⚠️ 发现image类型元素！");
            System.out.println("    完整数据: " + JSON.toJSONString(item, true));
        }
        
        // 特别关注包含二维码或条码相关的sub_type
        if (subType != null && (subType.contains("barcode") || subType.contains("qr") || subType.contains("code"))) {
            System.out.println("    ⚠️ 发现条码相关元素！");
            System.out.println("    完整数据: " + JSON.toJSONString(item, true));
        }
    }
    
    private void analyzeStructuredItem(int index, Map<String, Object> item) {
        String subType = (String) item.get("sub_type");
        List<Integer> content = (List<Integer>) item.get("content");
        
        System.out.println("\n  Structured[" + index + "]:");
        System.out.println("    sub_type: " + subType);
        System.out.println("    content: " + content);
        System.out.println("    所有字段: " + item.keySet());
        
        // 特别关注条码相关的structured元素
        if (subType != null && (subType.contains("barcode") || subType.contains("qr") || subType.contains("code"))) {
            System.out.println("    ⚠️ 发现条码相关structured元素！");
            System.out.println("    完整数据: " + JSON.toJSONString(item, true));
        }
    }
}