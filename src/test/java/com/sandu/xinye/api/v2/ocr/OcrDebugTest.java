package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.Map;

/**
 * OCR调试测试
 * 用于检查TextIn API的实际返回数据
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class OcrDebugTest {
    
    private TextInApiClient textInApiClient;
    
    @Before
    public void setUp() {
        // 加载配置文件
        PropKit.use("common_config.txt");
        textInApiClient = TextInApiClient.me;
    }
    
    @Test
    public void testFullOcrProcessing() {
        try {
            // 使用真实的测试图片
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\肥牛卷（40_30）.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("测试图片不存在: " + imagePath);
                return;
            }
            
            System.out.println("开始完整OCR处理流程测试...");
            System.out.println("图片路径: " + imagePath);
            System.out.println("图片大小: " + imageFile.length() + " bytes");
            
            // 1. 首先测试TextIn API调用
            TextInResponse textInResponse = textInApiClient.recognizeImage(imageFile);
            
            System.out.println("\n=== TextIn API调用结果 ===");
            System.out.println("响应代码: " + textInResponse.getCode());
            System.out.println("响应消息: " + textInResponse.getMessage());
            
            if (textInResponse.getPages() != null) {
                System.out.println("页面数量: " + textInResponse.getPages().size());
            }
            
            // 2. 然后测试ElementConverter转换
            System.out.println("\n=== 开始ElementConverter转换 ===");
            ElementConverter converter = ElementConverter.me;
            
            // 模拟转换过程
            OcrResponse ocrResponse = converter.convertToXPrinterFormat(
                textInResponse, imageFile, 480, 360);
            
            System.out.println("\n=== 转换结果 ===");
            System.out.println("识别元素数量: " + ocrResponse.getElements().size());
            System.out.println("图片信息: " + ocrResponse.getImageInfo());
            
            // 显示每个元素的详细信息
            for (int i = 0; i < ocrResponse.getElements().size(); i++) {
                Map<String, Object> element = ocrResponse.getElements().get(i);
                System.out.println("元素[" + i + "]: " + element);
            }
            
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}