package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 表格单元格样式信息测试
 * 验证表格单元格是否包含charWidth等详细样式信息
 * 
 * <AUTHOR>  
 * @date 2025-08-08
 */
public class TableCellStyleTest {
    
    private OcrService ocrService;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        ocrService = OcrService.me;
    }
    
    @Test
    public void testTableCellStyleInformation() {
        try {
            System.out.println("📋 测试表格单元格样式信息");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\鞋子（60_40）-2.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 使用新架构进行OCR
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, "鞋子（60_40）-2.png");
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (!result.isTrue("success")) {
                System.out.println("❌ OCR服务调用失败: " + result.getStr("msg"));
                return;
            }
            
            OcrResponse ocrResponse = (OcrResponse) result.get("data");
            analyzeTableCellStyles(ocrResponse);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 分析表格单元格的样式信息
     */
    @SuppressWarnings("unchecked")
    private void analyzeTableCellStyles(OcrResponse ocrResponse) {
        System.out.println("\\n📊 表格单元格样式信息分析:");
        
        List<Map<String, Object>> elements = ocrResponse.getElements();
        boolean foundTable = false;
        
        for (Map<String, Object> element : elements) {
            String elementType = String.valueOf(element.get("elementType"));
            
            if ("10".equals(elementType)) { // 表格类型
                foundTable = true;
                System.out.println("✅ 发现表格元素");
                System.out.println("  行数: " + element.get("rowCount"));
                System.out.println("  列数: " + element.get("colCount"));
                
                List<Map<String, Object>> cells = (List<Map<String, Object>>) element.get("cells");
                if (cells != null && !cells.isEmpty()) {
                    System.out.println("  单元格数量: " + cells.size());
                    
                    System.out.println("\\n📋 单元格详细样式信息:");
                    for (int i = 0; i < Math.min(cells.size(), 10); i++) {
                        Map<String, Object> cell = cells.get(i);
                        
                        String content = String.valueOf(cell.get("content"));
                        Integer row = (Integer) cell.get("row");
                        Integer col = (Integer) cell.get("col");
                        
                        System.out.println("  单元格[" + i + "] (" + row + "," + col + "): '" + content + "'");
                        
                        // 检查所有样式信息（包括新增的属性）
                        Double charWidth = (Double) cell.get("charWidth");
                        String alignment = (String) cell.get("alignment");
                        Boolean bold = (Boolean) cell.get("bold");
                        Boolean italic = (Boolean) cell.get("italic");
                        Integer width = (Integer) cell.get("width");
                        Integer height = (Integer) cell.get("height");
                        Boolean hasImage = (Boolean) cell.get("hasImage");
                        
                        // 其他属性
                        Double rotationAngle = (Double) cell.get("rotationAngle");
                        String textDirection = (String) cell.get("textDirection");
                        Double confidence = (Double) cell.get("confidence");
                        Boolean isHandwritten = (Boolean) cell.get("isHandwritten");
                        
                        System.out.println("    📝 新增样式属性:");
                        System.out.println("      alignment: " + alignment);
                        System.out.println("      bold: " + bold);
                        System.out.println("      italic: " + italic);
                        System.out.println("      width: " + width);
                        System.out.println("      height: " + height);
                        System.out.println("      hasImage: " + hasImage);
                        
                        System.out.println("    📝 其他样式信息:");
                        System.out.println("      charWidth: " + charWidth);
                        System.out.println("      rotationAngle: " + rotationAngle);
                        System.out.println("      textDirection: " + textDirection);
                        System.out.println("      confidence: " + confidence);
                        System.out.println("      isHandwritten: " + isHandwritten);
                        
                        // 验证样式信息的有效性
                        validateCellStyleData(cell, content, i);
                        System.out.println();
                    }
                } else {
                    System.out.println("❌ 表格没有单元格数据");
                }
            }
        }
        
        if (!foundTable) {
            System.out.println("❌ 未发现表格元素");
        }
    }
    
    /**
     * 验证单元格样式数据的有效性（增强版：包含新增属性验证）
     */
    private void validateCellStyleData(Map<String, Object> cell, String content, int index) {
        System.out.println("    🔍 样式验证:");
        
        // 验证新增属性
        validateNewStyleAttributes(cell, content);
        
        // 验证原有属性
        validateOriginalStyleAttributes(cell, content);
    }
    
    /**
     * 验证新增的样式属性
     */
    private void validateNewStyleAttributes(Map<String, Object> cell, String content) {
        // 验证alignment
        String alignment = (String) cell.get("alignment");
        if (alignment != null && (alignment.equals("left") || alignment.equals("center") || alignment.equals("right"))) {
            System.out.println("      ✅ alignment有效: " + alignment);
        } else {
            System.out.println("      ❌ alignment无效: " + alignment);
        }
        
        // 验证bold和italic
        Boolean bold = (Boolean) cell.get("bold");
        Boolean italic = (Boolean) cell.get("italic");
        if (bold != null && italic != null) {
            System.out.println("      ✅ bold/italic属性完整: bold=" + bold + ", italic=" + italic);
        } else {
            System.out.println("      ❌ bold/italic属性缺失");
        }
        
        // 验证图片检测属性
        Boolean hasImage = (Boolean) cell.get("hasImage");
        if (hasImage != null) {
            if (hasImage) {
                System.out.println("      🖼️ 单元格包含图片内容: " + content);
            } else {
                System.out.println("      ✅ 单元格不包含图片内容");
            }
        } else {
            System.out.println("      ❌ hasImage属性缺失");
        }
        
        // 验证width和height
        Integer width = (Integer) cell.get("width");
        Integer height = (Integer) cell.get("height");
        if (width != null && height != null) {
            if (width >= 0 && height >= 0) {
                System.out.println("      ✅ 文本块尺寸有效: " + width + "x" + height);
                
                // 如果有内容但尺寸为0，可能是获取失败
                if (content != null && !content.trim().isEmpty() && !content.equals("null")) {
                    if (width > 0 && height > 0) {
                        System.out.println("      ✅ 文本块尺寸合理（有内容且尺寸>0）");
                    } else {
                        System.out.println("      ⚠️ 有内容但文本块尺寸为0，可能是获取失败");
                    }
                }
            } else {
                System.out.println("      ❌ 文本块尺寸无效: " + width + "x" + height);
            }
        } else {
            System.out.println("      ❌ 文本块尺寸缺失");
        }
    }
    
    /**
     * 验证原有的样式属性
     */
    private void validateOriginalStyleAttributes(Map<String, Object> cell, String content) {
        // 验证charWidth
        Double charWidth = (Double) cell.get("charWidth");
        if (charWidth != null && charWidth > 0) {
            System.out.println("      ✅ charWidth有效: " + charWidth);
            
            // 如果有内容，检查charWidth是否合理
            if (content != null && !content.trim().isEmpty() && !content.equals("null")) {
                double expectedMinWidth = 5.0; // 最小字符宽度
                double expectedMaxWidth = 50.0; // 最大字符宽度
                if (charWidth >= expectedMinWidth && charWidth <= expectedMaxWidth) {
                    System.out.println("      ✅ charWidth在合理范围内: " + expectedMinWidth + "-" + expectedMaxWidth);
                } else {
                    System.out.println("      ⚠️ charWidth可能不合理: " + charWidth);
                }
            }
        } else {
            System.out.println("      ❌ charWidth无效或为空");
        }
        
        // 验证confidence
        Double confidence = (Double) cell.get("confidence");
        if (confidence != null && confidence >= 0.0 && confidence <= 1.0) {
            System.out.println("      ✅ confidence有效: " + confidence);
        } else {
            System.out.println("      ❌ confidence无效: " + confidence);
        }
        
        // 验证兼容性布尔值
        Boolean isBold = (Boolean) cell.get("isBold");
        Boolean isItalic = (Boolean) cell.get("isItalic");
        Boolean isHandwritten = (Boolean) cell.get("isHandwritten");
        
        if (isBold != null && isItalic != null && isHandwritten != null) {
            System.out.println("      ✅ 兼容性布尔属性完整");
        } else {
            System.out.println("      ❌ 部分兼容性布尔属性缺失");
        }
        
        // 检查是否成功从TextIn提取了信息
        analyzeDataExtractionSuccess(cell, confidence, isBold, isItalic);
    }
    
    /**
     * 分析数据提取成功情况
     */
    private void analyzeDataExtractionSuccess(Map<String, Object> cell, Double confidence, Boolean isBold, Boolean isItalic) {
        boolean hasNonDefaultStyle = false;
        
        if (isBold != null && isBold) {
            hasNonDefaultStyle = true;
            System.out.println("      🎯 检测到粗体样式");
        }
        
        if (isItalic != null && isItalic) {
            hasNonDefaultStyle = true;
            System.out.println("      🎯 检测到斜体样式");
        }
        
        if (confidence != null && confidence < 1.0 && confidence > 0.0) {
            hasNonDefaultStyle = true;
            System.out.println("      🎯 检测到真实置信度: " + confidence);
        }
        
        // 检查是否有有效的文本块尺寸
        Integer width = (Integer) cell.get("width");
        Integer height = (Integer) cell.get("height");
        if (width != null && height != null && width > 0 && height > 0) {
            hasNonDefaultStyle = true;
            System.out.println("      🎯 成功提取文本块尺寸: " + width + "x" + height);
        }
        
        // 检查对齐方式是否智能计算
        String alignment = (String) cell.get("alignment");
        if (alignment != null && !alignment.equals("left")) { // left是默认值
            hasNonDefaultStyle = true;
            System.out.println("      🎯 智能计算出非默认对齐: " + alignment);
        }
        
        if (hasNonDefaultStyle) {
            System.out.println("      🎉 成功从TextIn提取了丰富的样式信息！");
        } else {
            System.out.println("      📝 主要使用了默认样式信息");
        }
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("table_cell_style_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}