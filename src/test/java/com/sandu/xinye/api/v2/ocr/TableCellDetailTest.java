package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 表格单元格详细信息测试
 * 分析TextIn返回的表格单元格数据结构，确定是否包含charWidth等样式信息
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class TableCellDetailTest {
    
    private TextInApiClient textInApiClient;
    
    @Before
    public void setUp() {
        PropKit.use("common_config.txt");
        textInApiClient = TextInApiClient.me;
    }
    
    @Test
    public void analyzeTableCellStructure() {
        try {
            System.out.println("🔍 分析表格单元格的详细数据结构");
            
            String imagePath = "D:\\workspace\\xprinter-backend\\data\\鞋子（60_40）-2.png";
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("⚠️ 测试图片不存在: " + imagePath);
                return;
            }
            
            // 调用TextIn API
            TextInResponse rawResponse = textInApiClient.recognizeImage(imageFile);
            
            if (rawResponse == null) {
                System.out.println("❌ TextIn API调用失败");
                return;
            }
            
            // 分析detail数组中的表格信息
            analyzeDetailTableStructure(rawResponse);
            
            // 分析pages[0].structured数组中的表格信息
            analyzeStructuredTableData(rawResponse);
            
            // 分析pages[0].content数组，看表格相关的content元素
            analyzeContentForTableCells(rawResponse);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 分析detail数组中的表格结构
     */
    @SuppressWarnings("unchecked")
    private void analyzeDetailTableStructure(TextInResponse response) {
        System.out.println("\n📋 分析Detail数组中的表格:");
        
        List<Map<String, Object>> detailList = response.getDetail();
        if (detailList == null) {
            System.out.println("Detail数组为空");
            return;
        }
        
        for (Map<String, Object> detail : detailList) {
            String type = (String) detail.get("type");
            if ("table".equals(type)) {
                System.out.println("发现表格detail元素:");
                System.out.println("  type: " + detail.get("type"));
                System.out.println("  sub_type: " + detail.get("sub_type"));
                System.out.println("  content: " + detail.get("content"));
                System.out.println("  position: " + detail.get("position"));
                
                // 分析cells数组
                List<Map<String, Object>> cells = (List<Map<String, Object>>) detail.get("cells");
                if (cells != null) {
                    System.out.println("  cells数组: " + cells.size() + "个单元格");
                    
                    // 详细分析前几个单元格
                    for (int i = 0; i < Math.min(cells.size(), 3); i++) {
                        Map<String, Object> cell = cells.get(i);
                        System.out.println("    单元格[" + i + "]:");
                        System.out.println("      所有字段: " + cell.keySet());
                        
                        for (Map.Entry<String, Object> entry : cell.entrySet()) {
                            System.out.println("      " + entry.getKey() + ": " + entry.getValue());
                        }
                        System.out.println();
                    }
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 分析structured数组中的表格数据
     */
    @SuppressWarnings("unchecked")
    private void analyzeStructuredTableData(TextInResponse response) {
        System.out.println("\n🏗️ 分析Structured数组中的表格:");
        
        List<Map<String, Object>> pages = response.getPages();
        if (pages == null || pages.isEmpty()) {
            return;
        }
        
        Map<String, Object> firstPage = pages.get(0);
        List<Map<String, Object>> structuredList = (List<Map<String, Object>>) firstPage.get("structured");
        
        if (structuredList == null) {
            System.out.println("Structured数组为空");
            return;
        }
        
        for (Map<String, Object> structured : structuredList) {
            String type = (String) structured.get("type");
            if ("table".equals(type)) {
                System.out.println("发现structured表格元素:");
                System.out.println("  所有字段: " + structured.keySet());
                
                for (Map.Entry<String, Object> entry : structured.entrySet()) {
                    System.out.println("  " + entry.getKey() + ": " + entry.getValue());
                }
                
                // 特别关注cells数组
                List<Map<String, Object>> cells = (List<Map<String, Object>>) structured.get("cells");
                if (cells != null) {
                    System.out.println("  structured cells详情:");
                    for (int i = 0; i < Math.min(cells.size(), 2); i++) {
                        Map<String, Object> cell = cells.get(i);
                        System.out.println("    structured单元格[" + i + "]:");
                        System.out.println("      所有字段: " + cell.keySet());
                        
                        for (Map.Entry<String, Object> entry : cell.entrySet()) {
                            System.out.println("      " + entry.getKey() + ": " + entry.getValue());
                        }
                        System.out.println();
                    }
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 分析content数组，查找表格相关的content元素
     */
    @SuppressWarnings("unchecked")
    private void analyzeContentForTableCells(TextInResponse response) {
        System.out.println("\n📄 分析Content数组中与表格相关的元素:");
        
        List<Map<String, Object>> pages = response.getPages();
        if (pages == null || pages.isEmpty()) {
            return;
        }
        
        Map<String, Object> firstPage = pages.get(0);
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) firstPage.get("content");
        
        if (contentList == null) {
            System.out.println("Content数组为空");
            return;
        }
        
        System.out.println("Content数组总共" + contentList.size() + "个元素");
        
        // 查找可能与表格相关的content元素
        for (int i = 0; i < contentList.size(); i++) {
            Map<String, Object> content = contentList.get(i);
            String type = (String) content.get("type");
            String text = (String) content.get("text");
            
            System.out.println("  content[" + i + "]: type=" + type + ", text='" + text + "'");
            
            // 如果是line类型且看起来像表格单元格内容，详细分析
            if ("line".equals(type) && (text != null && (text.contains("货号") || text.contains("颜色") || text.contains("码号") || text.contains("材质") || text.contains("鞋型")))) {
                System.out.println("    可能的表格单元格content:");
                System.out.println("      所有字段: " + content.keySet());
                
                // 特别关注char_positions和样式信息
                List<List<Integer>> charPositions = (List<List<Integer>>) content.get("char_positions");
                if (charPositions != null) {
                    System.out.println("      char_positions: " + charPositions.size() + "个字符");
                    System.out.println("      前3个字符位置: " + charPositions.subList(0, Math.min(3, charPositions.size())));
                }
                
                System.out.println("      angle: " + content.get("angle"));
                System.out.println("      direction: " + content.get("direction"));
                System.out.println("      score: " + content.get("score"));
                System.out.println("      handwritten: " + content.get("handwritten"));
                System.out.println("      pos: " + content.get("pos"));
                System.out.println();
            }
        }
    }
}