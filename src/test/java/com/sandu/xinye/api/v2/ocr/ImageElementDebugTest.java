package com.sandu.xinye.api.v2.ocr;

import org.junit.Before;
import org.junit.Test;
import java.io.File;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;

import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import java.util.Map;
import java.util.List;

/**
 * 图片元素调试测试
 * 专门用于调试设备标识3图片的识别问题
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class ImageElementDebugTest {
    
    private String testImagePath = "D:\\workspace\\xprinter-backend\\data\\设备标识3（40_30）.png";
    
    @Before
    public void setUp() {
        System.out.println("=== 图片元素识别调试测试 ===");
    }
    
    @Test
    public void testDeviceImageRecognitionDebug() {
        try {
            File imageFile = new File(testImagePath);
            if (!imageFile.exists()) {
                System.out.println("❌ 测试图片不存在: " + testImagePath);
                return;
            }
            
            // 检查图片基本信息
            System.out.println("📸 图片信息:");
            System.out.println("  路径: " + testImagePath);
            System.out.println("  大小: " + imageFile.length() + " bytes");
            
            try {
                BufferedImage img = ImageIO.read(imageFile);
                System.out.println("  尺寸: " + img.getWidth() + "x" + img.getHeight());
            } catch (Exception e) {
                System.out.println("  无法读取图片尺寸: " + e.getMessage());
            }
            
            System.out.println();
            
            // 创建安全的UploadFile模拟对象
            SafeUploadFile uploadFile = new SafeUploadFile(imageFile, imageFile.getName());
            
            // 调用OcrService进行识别
            System.out.println("🔄 开始调用OCR服务...");
            OcrService ocrService = OcrService.me;
            RetKit result = ocrService.recognizeImage(uploadFile, null, null);
            
            if (result.isTrue("success")) {
                System.out.println("✅ 识别成功!");
                
                OcrResponse response = (OcrResponse) result.get("data");
                List<Map<String, Object>> elements = response.getElements();
                
                System.out.println("📊 识别结果统计:");
                System.out.println("  总元素数量: " + elements.size());
                
                // 统计各类型元素
                int textCount = 0, barcodeCount = 0, qrcodeCount = 0, tableCount = 0, imageCount = 0;
                
                for (Map<String, Object> element : elements) {
                    String elementType = (String) element.get("elementType");
                    
                    switch (elementType) {
                        case "1": textCount++; break;
                        case "2": barcodeCount++; break;
                        case "7": qrcodeCount++; break;
                        case "8": imageCount++; break;
                        case "10": tableCount++; break;
                    }
                }
                
                System.out.println("  文本元素(1): " + textCount + "个");
                System.out.println("  条形码(2): " + barcodeCount + "个");
                System.out.println("  二维码(7): " + qrcodeCount + "个");
                System.out.println("  图片元素(8): " + imageCount + "个 ⭐");
                System.out.println("  表格(10): " + tableCount + "个");
                
                System.out.println("\n📋 详细元素列表:");
                for (int i = 0; i < elements.size(); i++) {
                    Map<String, Object> element = elements.get(i);
                    String elementType = (String) element.get("elementType");
                    
                    System.out.println("  [" + (i+1) + "] elementType: " + elementType);
                    
                    if ("1".equals(elementType)) {
                        // 文本元素
                        System.out.println("      内容: \"" + element.get("content") + "\"");
                        System.out.println("      位置: (" + element.get("x") + "," + element.get("y") + 
                                         ") 尺寸: " + element.get("width") + "x" + element.get("height"));
                        
                    } else if ("8".equals(elementType)) {
                        // 图片元素 - 重点关注
                        System.out.println("      🎯 这是图片元素!");
                        System.out.println("      imageUrl: " + element.get("imageUrl"));
                        System.out.println("      imageType: " + element.get("imageType"));
                        System.out.println("      confidence: " + element.get("confidence"));
                        System.out.println("      位置: (" + element.get("x") + "," + element.get("y") + 
                                         ") 尺寸: " + element.get("width") + "x" + element.get("height"));
                        
                    } else {
                        // 其他元素
                        System.out.println("      内容: " + element.get("content"));
                        System.out.println("      位置: (" + element.get("x") + "," + element.get("y") + 
                                         ") 尺寸: " + element.get("width") + "x" + element.get("height"));
                    }
                    System.out.println();
                }
                
                // 问题分析
                System.out.println("🔍 问题分析:");
                if (imageCount == 0) {
                    System.out.println("❌ 确实没有识别到图片元素(elementType=8)");
                    System.out.println("可能原因:");
                    System.out.println("1. TextIn API没有返回图片类型的数据");
                    System.out.println("2. ElementConverter中的图片处理逻辑有问题");
                    System.out.println("3. 图片质量不足，TextIn无法识别logo");
                    System.out.println("4. 配置问题导致图片识别功能未启用");
                } else {
                    System.out.println("✅ 成功识别到 " + imageCount + " 个图片元素");
                }
                
            } else {
                System.out.println("❌ 识别失败: " + result.getMsg());
                
                // 检查常见失败原因
                String errorMsg = result.getMsg();
                if (errorMsg != null) {
                    if (errorMsg.contains("TextIn")) {
                        System.out.println("💡 建议: 检查TextIn API配置和网络连接");
                    } else if (errorMsg.contains("文件")) {
                        System.out.println("💡 建议: 检查文件格式和大小");
                    } else if (errorMsg.contains("API")) {
                        System.out.println("💡 建议: 检查API密钥和应用ID");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 安全的上传文件实现，防止删除原始文件
     */
    private static class SafeUploadFile extends UploadFile {
        private File tempFile;
        private String fileName;
        
        public SafeUploadFile(File originalFile, String fileName) throws Exception {
            super("file", "file", fileName, fileName, "image/png");
            this.fileName = fileName;
            
            // 创建临时文件副本
            this.tempFile = File.createTempFile("debug_test_", ".png");
            java.nio.file.Files.copy(originalFile.toPath(), tempFile.toPath(), 
                                   java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        @Override
        public File getFile() {
            return tempFile;
        }
        
        @Override
        public String getFileName() {
            return fileName;
        }
    }
}