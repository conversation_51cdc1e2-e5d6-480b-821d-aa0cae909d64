package com.sandu.xinye.api.v2.ocr;

import org.junit.Test;
import java.lang.reflect.Method;

/**
 * 图片检测功能测试
 * 验证单元格中图片内容的检测逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class ImageDetectionTest {
    
    @Test
    public void testImageContentDetection() {
        try {
            System.out.println("🖼️ 测试图片内容检测功能");
            
            ElementConverter converter = ElementConverter.me;
            
            // 使用反射调用private方法进行测试
            Method detectMethod = ElementConverter.class.getDeclaredMethod("detectImageContentInCell", String.class);
            detectMethod.setAccessible(true);
            
            // 测试用例1：标准HTML img标签
            String imgTag = "<img src=\"https://web-api.textin.com/ocr_image/external/8e6da4df99960b61.jpg\">";
            boolean result1 = (Boolean) detectMethod.invoke(converter, imgTag);
            System.out.println("测试1 - HTML img标签: " + imgTag);
            System.out.println("检测结果: " + (result1 ? "✅ 检测到图片" : "❌ 未检测到图片"));
            
            // 测试用例2：图片URL
            String imgUrl = "https://web-api.textin.com/ocr_image/external/abc123def456.png";
            boolean result2 = (Boolean) detectMethod.invoke(converter, imgUrl);
            System.out.println("\n测试2 - 图片URL: " + imgUrl);
            System.out.println("检测结果: " + (result2 ? "✅ 检测到图片" : "❌ 未检测到图片"));
            
            // 测试用例3：普通文本
            String normalText = "货号";
            boolean result3 = (Boolean) detectMethod.invoke(converter, normalText);
            System.out.println("\n测试3 - 普通文本: " + normalText);
            System.out.println("检测结果: " + (result3 ? "❌ 误检测为图片" : "✅ 正确识别为文本"));
            
            // 测试用例4：包含图片URL的混合内容
            String mixedContent = "商品图片: https://web-api.textin.com/ocr_image/external/product123.jpg";
            boolean result4 = (Boolean) detectMethod.invoke(converter, mixedContent);
            System.out.println("\n测试4 - 混合内容: " + mixedContent);
            System.out.println("检测结果: " + (result4 ? "✅ 检测到图片" : "❌ 未检测到图片"));
            
            // 测试用例5：空内容
            String emptyContent = "";
            boolean result5 = (Boolean) detectMethod.invoke(converter, emptyContent);
            System.out.println("\n测试5 - 空内容: (空字符串)");
            System.out.println("检测结果: " + (result5 ? "❌ 误检测为图片" : "✅ 正确识别为非图片"));
            
            // 测试用例6：其他格式的图片URL
            String otherImgUrl = "https://example.com/image.png";
            boolean result6 = (Boolean) detectMethod.invoke(converter, otherImgUrl);
            System.out.println("\n测试6 - 其他格式图片URL: " + otherImgUrl);
            System.out.println("检测结果: " + (result6 ? "✅ 检测到图片" : "❌ 未检测到图片"));
            
            // 测试用例7：包含ocr_image关键词
            String ocrImageText = "ocr_image/external/test.jpg";
            boolean result7 = (Boolean) detectMethod.invoke(converter, ocrImageText);
            System.out.println("\n测试7 - 包含ocr_image: " + ocrImageText);
            System.out.println("检测结果: " + (result7 ? "✅ 检测到图片" : "❌ 未检测到图片"));
            
            // 汇总测试结果
            System.out.println("\n📊 测试结果汇总:");
            System.out.println("HTML img标签检测: " + (result1 ? "✅" : "❌"));
            System.out.println("TextIn图片URL检测: " + (result2 ? "✅" : "❌"));
            System.out.println("普通文本过滤: " + (!result3 ? "✅" : "❌"));
            System.out.println("混合内容检测: " + (result4 ? "✅" : "❌"));
            System.out.println("空内容处理: " + (!result5 ? "✅" : "❌"));
            System.out.println("通用图片URL检测: " + (result6 ? "✅" : "❌"));
            System.out.println("关键词检测: " + (result7 ? "✅" : "❌"));
            
            int passCount = 0;
            if (result1) passCount++;
            if (result2) passCount++;
            if (!result3) passCount++;
            if (result4) passCount++;
            if (!result5) passCount++;
            if (result6) passCount++;
            if (result7) passCount++;
            
            System.out.println("\n🎯 总体通过率: " + passCount + "/7 (" + String.format("%.1f", passCount * 100.0 / 7) + "%)");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}