package com.sandu.xinye.common.kit;

import com.jfinal.kit.PropKit;
import org.junit.Before;
import org.junit.Test;

/**
 * 代理商推广码验证工具类测试
 * 
 * <AUTHOR> Team
 * @since 2024-08-06
 */
public class AgentPromotionKitTest {
    
    @Before
    public void setUp() {
        // 确保配置文件已加载
        try {
            PropKit.use("common_config.txt");
        } catch (Exception e) {
            // 如果配置文件未找到，使用默认值
            System.out.println("配置文件未找到，使用默认配置进行测试");
        }
    }
    
    @Test
    public void testVerifyPromotionCodeWithValidCode() {
        System.out.println("=== 测试有效推广码验证 ===");
        
        // 使用你提供的示例推广码进行测试
        Integer testUserId = 12345;
        String testPromotionCode = "DU2UBA";
        
        System.out.println("测试参数：");
        System.out.println("- userId: " + testUserId);
        System.out.println("- promotionCode: " + testPromotionCode);
        System.out.println("- 接口URL: " + PropKit.get("promotion.agent.verify.url", "https://console-api.xpyun.net/api/admin/member/verifyPromotion"));
        System.out.println("- 签名算法: " + AgentPromotionKit.getSignAlgorithmDescription());
        
        Boolean result = AgentPromotionKit.verifyPromotionCode(testUserId, testPromotionCode);
        
        System.out.println("验证结果: " + result);
        if (result == null) {
            System.out.println("验证失败：接口调用异常或功能未启用");
        } else if (result) {
            System.out.println("验证成功：推广码存在");
        } else {
            System.out.println("验证失败：推广码不存在");
        }
    }
    
    @Test
    public void testVerifyPromotionCodeWithInvalidCode() {
        System.out.println("\n=== 测试无效推广码验证 ===");
        
        // 测试一个肯定不存在的推广码
        Integer testUserId = 12345;
        String testPromotionCode = "INVALID123";
        
        System.out.println("测试参数：");
        System.out.println("- userId: " + testUserId);
        System.out.println("- promotionCode: " + testPromotionCode);
        
        Boolean result = AgentPromotionKit.verifyPromotionCode(testUserId, testPromotionCode);
        
        System.out.println("验证结果: " + result);
        if (result == null) {
            System.out.println("验证失败：接口调用异常或功能未启用");
        } else if (result) {
            System.out.println("验证成功：推广码存在（意外结果）");
        } else {
            System.out.println("验证失败：推广码不存在（符合预期）");
        }
    }
    
    @Test
    public void testAgentVerifyAvailability() {
        System.out.println("\n=== 测试代理商接口可用性 ===");
        
        boolean isAvailable = AgentPromotionKit.isAgentVerifyAvailable();
        
        System.out.println("接口可用性: " + isAvailable);
        if (isAvailable) {
            System.out.println("代理商验证接口正常工作");
        } else {
            System.out.println("代理商验证接口无法访问，可能的原因：");
            System.out.println("1. 网络连接问题");
            System.out.println("2. 接口地址错误");
            System.out.println("3. 接口服务暂时不可用");
            System.out.println("4. 签名算法错误");
        }
    }
    
    @Test
    public void testConfiguration() {
        System.out.println("\n=== 配置信息测试 ===");
        
        System.out.println("是否启用验证: " + AgentPromotionKit.isVerifyEnabled());
        System.out.println("验证失败时允许绑定: " + AgentPromotionKit.isFailoverEnabled());
        System.out.println("签名算法: " + AgentPromotionKit.getSignAlgorithmDescription());
        
        // 测试签名生成
        long timestamp = System.currentTimeMillis();
        System.out.println("\n签名测试:");
        System.out.println("- 时间戳: " + timestamp);
        System.out.println("- 用户ID: 12345");
        System.out.println("- 签名原文: 12345123!@#wqee" + timestamp);
    }
    
    @Test
    public void testParameterValidation() {
        System.out.println("\n=== 参数验证测试 ===");
        
        // 测试null参数
        Boolean result1 = AgentPromotionKit.verifyPromotionCode(null, "TEST123");
        System.out.println("userId为null时的结果: " + result1);
        
        Boolean result2 = AgentPromotionKit.verifyPromotionCode(12345, null);
        System.out.println("promotionCode为null时的结果: " + result2);
        
        Boolean result3 = AgentPromotionKit.verifyPromotionCode(12345, "");
        System.out.println("promotionCode为空字符串时的结果: " + result3);
        
        Boolean result4 = AgentPromotionKit.verifyPromotionCode(12345, "   ");
        System.out.println("promotionCode为空白字符时的结果: " + result4);
    }
}