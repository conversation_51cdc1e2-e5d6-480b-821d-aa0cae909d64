package com.sandu.xinye.promotion;

import com.sandu.xinye.common.kit.PromotionCodeKit;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 推广码功能测试类
 * 
 * <AUTHOR> Team
 * @since 2024-07-30
 */
public class PromotionCodeTest {
    
    @Test
    public void testPromotionCodeValidation() {
        // 测试有效的推广码格式
        assertTrue("6位字母数字组合应该有效", PromotionCodeKit.isValidFormat("ABC123"));
        assertTrue("8位字母数字组合应该有效", PromotionCodeKit.isValidFormat("XY789ABC"));
        assertTrue("全字母应该有效", PromotionCodeKit.isValidFormat("ABCDEF"));
        assertTrue("全数字应该有效", PromotionCodeKit.isValidFormat("123456"));
        
        // 测试无效的推广码格式
        assertFalse("空字符串应该无效", PromotionCodeKit.isValidFormat(""));
        assertFalse("null应该无效", PromotionCodeKit.isValidFormat(null));
        assertFalse("5位字符应该无效", PromotionCodeKit.isValidFormat("ABC12"));
        assertFalse("9位字符应该无效", PromotionCodeKit.isValidFormat("ABC123456"));
        assertFalse("包含特殊字符应该无效", PromotionCodeKit.isValidFormat("ABC-123"));
        assertFalse("包含空格应该无效", PromotionCodeKit.isValidFormat("ABC 123"));
        assertFalse("包含中文应该无效", PromotionCodeKit.isValidFormat("ABC中文"));
    }
    
    @Test
    public void testPromotionCodeNormalization() {
        // 测试推广码标准化
        assertEquals("应该转换为大写", "ABC123", PromotionCodeKit.normalize("abc123"));
        assertEquals("应该去除空格", "ABC123", PromotionCodeKit.normalize(" ABC123 "));
        assertEquals("混合大小写应该转换为大写", "XY789ABC", PromotionCodeKit.normalize("xy789AbC"));
        
        // 测试空值处理
        assertNull("空字符串应该返回null", PromotionCodeKit.normalize(""));
        assertNull("null应该返回null", PromotionCodeKit.normalize(null));
        assertNull("空格字符串应该返回null", PromotionCodeKit.normalize("   "));
    }
    
    @Test
    public void testValidateAndNormalize() {
        // 测试验证并标准化
        assertEquals("有效推广码应该返回标准化结果", "ABC123", PromotionCodeKit.validateAndNormalize("abc123"));
        assertEquals("有效推广码应该返回标准化结果", "XY789ABC", PromotionCodeKit.validateAndNormalize(" xy789AbC "));
        
        // 测试无效推广码
        assertNull("无效推广码应该返回null", PromotionCodeKit.validateAndNormalize("ABC-123"));
        assertNull("无效推广码应该返回null", PromotionCodeKit.validateAndNormalize("ABC12"));
        assertNull("空字符串应该返回null", PromotionCodeKit.validateAndNormalize(""));
    }
    
    @Test
    public void testIsBlankOrInvalid() {
        // 测试空值或无效检查
        assertTrue("空字符串应该返回true", PromotionCodeKit.isBlankOrInvalid(""));
        assertTrue("null应该返回true", PromotionCodeKit.isBlankOrInvalid(null));
        assertTrue("无效格式应该返回true", PromotionCodeKit.isBlankOrInvalid("ABC-123"));
        assertTrue("长度不符应该返回true", PromotionCodeKit.isBlankOrInvalid("ABC12"));
        
        // 测试有效推广码
        assertFalse("有效推广码应该返回false", PromotionCodeKit.isBlankOrInvalid("ABC123"));
        assertFalse("有效推广码应该返回false", PromotionCodeKit.isBlankOrInvalid("XY789ABC"));
    }
    
    @Test
    public void testErrorMessages() {
        // 测试错误信息
        String formatDescription = PromotionCodeKit.getFormatDescription();
        assertNotNull("格式说明不应该为空", formatDescription);
        assertTrue("格式说明应该包含关键信息", formatDescription.contains("6-8位"));
        
        String errorMessage = PromotionCodeKit.getFormatErrorMessage();
        assertNotNull("错误信息不应该为空", errorMessage);
        assertTrue("错误信息应该包含格式说明", errorMessage.contains(formatDescription));
    }
}
