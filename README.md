# 表单模板服务器 (Form Template Server)

本项目为管理表单模板的服务器。

包含后端API及系统管理后台(Web APP)

客户端APP可以通过API与服务器进行通讯从而实现模板的云端存取。

系统管理后台单独建立项目为 ftserver-admin

## 技术实现

后端采用Java语言及以下技术实现：

* [JFinal](https://jfinal.com/)

    整体的架构采用 JFinal，并使用其模板例如 sql 文件夹中的模板即采用了  Enjoy Template Engine 模块

* [Hutool](https://hutool.cn/)

## 构建

mvn compile

注意构建时，official_config.txt 中的相关配置与对应的厂商配置需要相匹配。

## 打包

mvn package

## 部署

本项目部署时中会分别对应以下四个不同厂商版本APP进行独立部署：

* soonMark (索马克)
* Xprinter (芯烨通用版)
* 4BarLabel (恒全、芯烨、创微 - 中性版本)
* xpbarcode(恒全，芯烨?）

部署步骤：

1. 把生成的 war 文件放置到 tomcat 的 webapps 目录下，tomcat 在运行的时候会自动解压缩出相应的目录。
2. 修改 server.xml
    * 把 Server 节点的 port 属性改为 厂商特定的 TOMCAT 关闭端口
    * 把 Server.Service[name="Catalina"].Connector 节点的 port 属性改为厂商特定的 APP 连接端口，这个是 API 及 webapp 调用的接口
    * 在 Server.Service[name="Catalina"].Engine[name="Catalina"].Host[name="localhost"] 节点下增加以下内容：

        `<Context path="/upload" docBase="${UPLOAD_DIR}/upload${CONNECTOR_PORT}" reloadable="true" debug="0" />`

        请替换 ${UPLOAD_DIR}  为在本机的绝对路径地址
        请替换 ${CONNECTOR_PORT} 为上述的 APP 连接端口

        **注意：**指定绝对路径的方式是不正确的做法！但是目前来说确实是如此部署。

## 技术分析

### 数据库模型生成

以下的类方法用于生成对应的数据库模型

GeneratorModel.main()

### 主入口

XinyeConfig.main()

### 连接信息

通过修改 common_config.txt 进行配置

### 隐私政策更新

在 tomcat 的 webapps 相对应目录下（解包之后），更新该文件需要重新打包然后解压覆盖原来的文件。

APP方要看到效果的话，需要清理系统缓存，才能刷新出来。

## 已知问题

* 功能重置密码未实现

    需要实现  `/admin/my/resetPassword`  相关的 Controller 及 Service

* 删除账号不会删除用户的相关文件

## 未来计划

[未来功能规划](./docs/future-plans.md)

## API

调用格式：

http://{HOST}:{PORT}/api/{API_CLASS}/{API_ACTION}

所有调用除了注册及登录的API外，均会带有以下的头部参数：

* accessToken

### 登录

API_CLASS: Login

#### 注销账号

API_ACTION : unregister

API_PARAMS:

* userId

